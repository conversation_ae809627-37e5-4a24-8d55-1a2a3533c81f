"""
File Size and Content Checker
Author: <PERSON><PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-20
Description: Check file sizes, content quality, and generate summary report
"""

import os
import pandas as pd
from pathlib import Path
import logging
from datetime import datetime
from docx import Document
from pptx import Presentation
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FileContentChecker:
    """
    Comprehensive file content and quality checker
    Analyzes generated reports and provides detailed summary
    """
    
    def __init__(self, output_directory: str = "output"):
        """Initialize the file checker"""
        self.output_dir = Path(output_directory)
        self.report_date = datetime.now().strftime("%Y-%m-%d")
        self.check_results = {}
        
        logger.info("File Content Checker initialized")
    
    def check_file_sizes(self):
        """Check sizes of all generated files"""
        logger.info("Checking file sizes...")
        
        file_sizes = {}
        
        # Check all files in output directory
        for file_path in self.output_dir.rglob("*"):
            if file_path.is_file():
                size_bytes = file_path.stat().st_size
                size_mb = size_bytes / (1024 * 1024)
                
                file_sizes[str(file_path.relative_to(self.output_dir))] = {
                    'size_bytes': size_bytes,
                    'size_mb': round(size_mb, 2),
                    'size_formatted': self.format_file_size(size_bytes)
                }
        
        self.check_results['file_sizes'] = file_sizes
        logger.info(f"Checked {len(file_sizes)} files")
        
        return file_sizes
    
    def format_file_size(self, size_bytes):
        """Format file size in human-readable format"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    
    def check_word_document_content(self):
        """Check Word document content and structure"""
        logger.info("Checking Word document content...")
        
        word_files = list(self.output_dir.rglob("*.docx"))
        word_analysis = {}
        
        for word_file in word_files:
            try:
                doc = Document(word_file)
                
                # Count paragraphs, pages (estimated), and words
                paragraph_count = len(doc.paragraphs)
                
                # Estimate word count
                word_count = 0
                for paragraph in doc.paragraphs:
                    word_count += len(paragraph.text.split())
                
                # Estimate page count (assuming ~250 words per page)
                estimated_pages = max(1, word_count // 250)
                
                # Check for headings and structure
                heading_count = 0
                for paragraph in doc.paragraphs:
                    if paragraph.style.name.startswith('Heading') or 'Heading' in paragraph.style.name:
                        heading_count += 1
                
                # Check for tables
                table_count = len(doc.tables)
                
                word_analysis[word_file.name] = {
                    'paragraph_count': paragraph_count,
                    'estimated_word_count': word_count,
                    'estimated_pages': estimated_pages,
                    'heading_count': heading_count,
                    'table_count': table_count,
                    'has_toc': any('table of contents' in p.text.lower() for p in doc.paragraphs),
                    'sections_identified': self.identify_word_sections(doc)
                }
                
            except Exception as e:
                logger.error(f"Error analyzing Word document {word_file}: {str(e)}")
                word_analysis[word_file.name] = {'error': str(e)}
        
        self.check_results['word_documents'] = word_analysis
        return word_analysis
    
    def identify_word_sections(self, doc):
        """Identify main sections in Word document"""
        sections = []
        
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            if (paragraph.style.name.startswith('Heading') or 'Heading' in paragraph.style.name) and text:
                sections.append(text[:100])  # First 100 characters
        
        return sections[:10]  # Return first 10 sections
    
    def check_powerpoint_content(self):
        """Check PowerPoint presentation content and structure"""
        logger.info("Checking PowerPoint presentation content...")
        
        ppt_files = list(self.output_dir.rglob("*.pptx"))
        ppt_analysis = {}
        
        for ppt_file in ppt_files:
            try:
                prs = Presentation(ppt_file)
                
                slide_count = len(prs.slides)
                
                # Analyze slide content
                slides_with_text = 0
                slides_with_images = 0
                total_text_length = 0
                slide_titles = []
                
                for slide in prs.slides:
                    has_text = False
                    has_images = False
                    
                    # Check for title
                    if hasattr(slide, 'shapes') and slide.shapes.title:
                        slide_titles.append(slide.shapes.title.text[:100])
                    
                    # Check shapes for text and images
                    for shape in slide.shapes:
                        if hasattr(shape, 'text') and shape.text.strip():
                            has_text = True
                            total_text_length += len(shape.text)
                        
                        if hasattr(shape, 'image'):
                            has_images = True
                    
                    if has_text:
                        slides_with_text += 1
                    if has_images:
                        slides_with_images += 1
                
                ppt_analysis[ppt_file.name] = {
                    'slide_count': slide_count,
                    'slides_with_text': slides_with_text,
                    'slides_with_images': slides_with_images,
                    'total_text_length': total_text_length,
                    'average_text_per_slide': total_text_length // max(1, slide_count),
                    'slide_titles': slide_titles[:15]  # First 15 slide titles
                }
                
            except Exception as e:
                logger.error(f"Error analyzing PowerPoint presentation {ppt_file}: {str(e)}")
                ppt_analysis[ppt_file.name] = {'error': str(e)}
        
        self.check_results['powerpoint_presentations'] = ppt_analysis
        return ppt_analysis
    
    def check_visualization_files(self):
        """Check visualization files (PNG, JPG, etc.)"""
        logger.info("Checking visualization files...")
        
        image_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg']
        visualization_files = []
        
        for ext in image_extensions:
            visualization_files.extend(list(self.output_dir.rglob(f"*{ext}")))
        
        viz_analysis = {}
        
        for viz_file in visualization_files:
            try:
                file_stats = viz_file.stat()
                
                viz_analysis[viz_file.name] = {
                    'file_type': viz_file.suffix,
                    'size_bytes': file_stats.st_size,
                    'size_formatted': self.format_file_size(file_stats.st_size),
                    'created_date': datetime.fromtimestamp(file_stats.st_ctime).strftime("%Y-%m-%d %H:%M:%S"),
                    'modified_date': datetime.fromtimestamp(file_stats.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
                }
                
            except Exception as e:
                logger.error(f"Error analyzing visualization file {viz_file}: {str(e)}")
                viz_analysis[viz_file.name] = {'error': str(e)}
        
        self.check_results['visualization_files'] = viz_analysis
        return viz_analysis
    
    def check_data_files(self):
        """Check data files (CSV, Excel, etc.)"""
        logger.info("Checking data files...")
        
        data_extensions = ['.csv', '.xlsx', '.xls', '.json', '.pkl', '.parquet']
        data_files = []
        
        for ext in data_extensions:
            data_files.extend(list(self.output_dir.rglob(f"*{ext}")))
        
        # Also check original data files in main directory
        main_dir = Path(".")
        for ext in ['.csv']:
            data_files.extend(list(main_dir.glob(f"*{ext}")))
        
        data_analysis = {}
        
        for data_file in data_files:
            try:
                file_stats = data_file.stat()
                
                analysis = {
                    'file_type': data_file.suffix,
                    'size_bytes': file_stats.st_size,
                    'size_formatted': self.format_file_size(file_stats.st_size),
                    'location': str(data_file.parent)
                }
                
                # Try to get basic info about CSV files
                if data_file.suffix == '.csv':
                    try:
                        df = pd.read_csv(data_file, nrows=0)  # Just get column info
                        analysis['columns'] = len(df.columns)
                        analysis['column_names'] = list(df.columns)[:10]  # First 10 columns
                    except:
                        analysis['csv_readable'] = False
                
                data_analysis[data_file.name] = analysis
                
            except Exception as e:
                logger.error(f"Error analyzing data file {data_file}: {str(e)}")
                data_analysis[data_file.name] = {'error': str(e)}
        
        self.check_results['data_files'] = data_analysis
        return data_analysis
    
    def generate_summary_report(self):
        """Generate comprehensive summary report"""
        logger.info("Generating summary report...")
        
        summary = {
            'report_date': self.report_date,
            'analysis_summary': {},
            'file_inventory': {},
            'quality_assessment': {},
            'recommendations': []
        }
        
        # File inventory summary
        total_files = len(self.check_results.get('file_sizes', {}))
        total_size_mb = sum(f['size_mb'] for f in self.check_results.get('file_sizes', {}).values())
        
        summary['file_inventory'] = {
            'total_files': total_files,
            'total_size_mb': round(total_size_mb, 2),
            'total_size_formatted': self.format_file_size(total_size_mb * 1024 * 1024),
            'file_types': self.get_file_type_distribution()
        }
        
        # Analysis summary
        word_docs = len(self.check_results.get('word_documents', {}))
        ppt_presentations = len(self.check_results.get('powerpoint_presentations', {}))
        visualizations = len(self.check_results.get('visualization_files', {}))
        data_files = len(self.check_results.get('data_files', {}))
        
        summary['analysis_summary'] = {
            'word_documents': word_docs,
            'powerpoint_presentations': ppt_presentations,
            'visualization_files': visualizations,
            'data_files': data_files
        }
        
        # Quality assessment
        summary['quality_assessment'] = self.assess_quality()
        
        # Recommendations
        summary['recommendations'] = self.generate_recommendations()
        
        # Save summary to JSON
        summary_path = self.output_dir / f"file_analysis_summary_{self.report_date}.json"
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        logger.info(f"Summary report saved: {summary_path}")
        return summary
    
    def get_file_type_distribution(self):
        """Get distribution of file types"""
        file_types = {}
        
        for file_path, file_info in self.check_results.get('file_sizes', {}).items():
            ext = Path(file_path).suffix.lower()
            if ext not in file_types:
                file_types[ext] = 0
            file_types[ext] += 1
        
        return file_types
    
    def assess_quality(self):
        """Assess overall quality of generated files"""
        quality_score = 100
        issues = []
        
        # Check Word documents
        for doc_name, doc_info in self.check_results.get('word_documents', {}).items():
            if 'error' in doc_info:
                quality_score -= 20
                issues.append(f"Error in Word document: {doc_name}")
            elif doc_info.get('estimated_pages', 0) < 30:
                quality_score -= 10
                issues.append(f"Word document may be too short: {doc_name}")
        
        # Check PowerPoint presentations
        for ppt_name, ppt_info in self.check_results.get('powerpoint_presentations', {}).items():
            if 'error' in ppt_info:
                quality_score -= 20
                issues.append(f"Error in PowerPoint presentation: {ppt_name}")
            elif ppt_info.get('slide_count', 0) < 15:
                quality_score -= 10
                issues.append(f"PowerPoint presentation may have too few slides: {ppt_name}")
        
        # Check visualizations
        if len(self.check_results.get('visualization_files', {})) < 3:
            quality_score -= 15
            issues.append("Insufficient number of visualization files")
        
        return {
            'overall_score': max(0, quality_score),
            'issues': issues,
            'status': 'Excellent' if quality_score >= 90 else 'Good' if quality_score >= 70 else 'Needs Improvement'
        }
    
    def generate_recommendations(self):
        """Generate recommendations based on analysis"""
        recommendations = []
        
        # File size recommendations
        large_files = []
        for file_path, file_info in self.check_results.get('file_sizes', {}).items():
            if file_info['size_mb'] > 50:
                large_files.append(file_path)
        
        if large_files:
            recommendations.append(f"Consider optimizing large files: {', '.join(large_files)}")
        
        # Content recommendations
        word_docs = self.check_results.get('word_documents', {})
        for doc_name, doc_info in word_docs.items():
            if doc_info.get('estimated_pages', 0) > 60:
                recommendations.append(f"Consider breaking down large document: {doc_name}")
        
        ppt_presentations = self.check_results.get('powerpoint_presentations', {})
        for ppt_name, ppt_info in ppt_presentations.items():
            if ppt_info.get('slide_count', 0) > 40:
                recommendations.append(f"Consider reducing slide count for better presentation flow: {ppt_name}")
        
        if not recommendations:
            recommendations.append("All files appear to be well-structured and appropriately sized")
        
        return recommendations
    
    def run_comprehensive_check(self):
        """Run all checks and generate comprehensive report"""
        logger.info("Starting comprehensive file content check...")
        
        try:
            # Run all checks
            self.check_file_sizes()
            self.check_word_document_content()
            self.check_powerpoint_content()
            self.check_visualization_files()
            self.check_data_files()
            
            # Generate summary
            summary = self.generate_summary_report()
            
            logger.info("Comprehensive file check completed successfully!")
            return summary
            
        except Exception as e:
            logger.error(f"Error during comprehensive check: {str(e)}")
            raise


def main():
    """Main function to run file content checker"""
    print("File Content and Quality Checker")
    print("=" * 40)
    
    try:
        checker = FileContentChecker()
        summary = checker.run_comprehensive_check()
        
        print("\nFile Analysis Summary:")
        print("-" * 30)
        print(f"Total Files: {summary['file_inventory']['total_files']}")
        print(f"Total Size: {summary['file_inventory']['total_size_formatted']}")
        print(f"Word Documents: {summary['analysis_summary']['word_documents']}")
        print(f"PowerPoint Presentations: {summary['analysis_summary']['powerpoint_presentations']}")
        print(f"Visualizations: {summary['analysis_summary']['visualization_files']}")
        print(f"Data Files: {summary['analysis_summary']['data_files']}")
        print(f"\nQuality Score: {summary['quality_assessment']['overall_score']}/100")
        print(f"Status: {summary['quality_assessment']['status']}")
        
        if summary['quality_assessment']['issues']:
            print("\nIssues Found:")
            for issue in summary['quality_assessment']['issues']:
                print(f"  • {issue}")
        
        print("\nRecommendations:")
        for rec in summary['recommendations']:
            print(f"  • {rec}")
        
        return summary
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return None


if __name__ == "__main__":
    summary = main()
