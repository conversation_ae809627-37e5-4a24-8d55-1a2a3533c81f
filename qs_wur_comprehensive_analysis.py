"""
QS World University Rankings Comprehensive Analysis
Author: <PERSON><PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-20
Description: Comprehensive analysis of QS WUR data (2022-2026) focusing on 
            Symbiosis International University's performance and strategic positioning
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from pathlib import Path
import logging
from datetime import datetime
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score
import scipy.stats as stats
from typing import Dict, List, Tuple, Optional
import re

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Suppress warnings
warnings.filterwarnings('ignore')

class QSWURAnalyzer:
    """
    Comprehensive QS World University Rankings Analyzer
    
    This class handles all aspects of QS WUR data analysis including:
    - Data loading and preprocessing
    - Symbiosis performance analysis
    - Competitive benchmarking
    - Predictive modeling
    - Visualization generation
    """
    
    def __init__(self, data_directory: str = "."):
        """
        Initialize the QS WUR Analyzer
        
        Parameters
        ----------
        data_directory : str
            Directory containing QS WUR CSV files
        """
        self.data_directory = Path(data_directory)
        self.years = [2022, 2023, 2024, 2025, 2026]
        self.data = {}
        self.combined_data = None
        self.symbiosis_data = None
        self.indian_institutions = None
        self.analysis_results = {}
        
        # Setup visualization style
        self.setup_visualization_style()
        
        logger.info("QS WUR Analyzer initialized successfully")
    
    def setup_visualization_style(self):
        """Set global styling for all visualizations."""
        plt.style.use('seaborn-v0_8-whitegrid')
        
        # Color palettes for different visualization types
        self.COLOR_PALETTE = {
            'categorical': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                           '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'],
            'sequential': sns.color_palette("viridis", 10),
            'diverging': sns.color_palette("RdBu_r", 10),
            'highlight': ['#cccccc', '#cccccc', '#cccccc', '#ff7f0e', '#cccccc'],
            'symbiosis': '#1f77b4',
            'private': '#ff7f0e',
            'government': '#2ca02c'
        }
        
        # Typography and sizing
        plt.rcParams.update({
            'font.family': 'sans-serif',
            'font.sans-serif': ['Arial', 'DejaVu Sans', 'Liberation Sans'],
            'font.size': 12,
            'axes.titlesize': 16,
            'axes.titleweight': 'bold',
            'axes.labelsize': 14,
            'xtick.labelsize': 12,
            'ytick.labelsize': 12,
            'figure.figsize': (12, 8),
            'figure.dpi': 100,
            'savefig.dpi': 300,
            'savefig.bbox': 'tight'
        })
        
        logger.info("Visualization styling configured")
    
    def load_dataset(self, filepath: str, year: int) -> pd.DataFrame:
        """
        Load and validate QS WUR dataset for a specific year

        Parameters
        ----------
        filepath : str
            Path to the CSV file
        year : int
            Year of the dataset

        Returns
        -------
        pd.DataFrame
            Loaded and validated dataset
        """
        try:
            # Try different encodings
            encodings = ['utf-8', 'latin-1', 'iso-8859-1', 'cp1252']
            df = None

            for encoding in encodings:
                try:
                    df = pd.read_csv(filepath, encoding=encoding)
                    logger.info(f"Successfully loaded {year} data with {encoding} encoding")
                    break
                except UnicodeDecodeError:
                    continue

            if df is None:
                raise ValueError(f"Could not load file with any of the attempted encodings: {encodings}")

            # Add year column
            df['Year'] = year

            # Basic validation
            logger.info(f"Loaded {year} data: {len(df)} institutions, {len(df.columns)} columns")

            # Log data quality metrics
            missing_percentage = (df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100
            logger.info(f"Missing data percentage for {year}: {missing_percentage:.2f}%")

            return df

        except Exception as e:
            logger.error(f"Error loading data for {year}: {str(e)}")
            raise
    
    def load_all_data(self):
        """Load all QS WUR datasets (2022-2026)"""
        logger.info("Starting data loading process...")
        
        file_patterns = {
            2022: "2022 QS World University Rankings.csv",
            2023: "2023 QS World University Rankings.csv", 
            2024: "2024 QS World University Rankings.csv",
            2025: "2025 QS World University Ranking.csv",
            2026: "2026 QS World University Rankings.csv"
        }
        
        for year in self.years:
            filepath = self.data_directory / file_patterns[year]
            if filepath.exists():
                self.data[year] = self.load_dataset(filepath, year)
            else:
                logger.warning(f"File not found for {year}: {filepath}")
        
        logger.info(f"Successfully loaded data for {len(self.data)} years")
        
        # Create combined dataset
        self.create_combined_dataset()
    
    def create_combined_dataset(self):
        """Create a combined dataset from all years"""
        logger.info("Creating combined dataset...")
        
        if not self.data:
            raise ValueError("No data loaded. Please run load_all_data() first.")
        
        # Combine all datasets
        combined_dfs = []
        for year, df in self.data.items():
            df_copy = df.copy()
            df_copy['Year'] = year
            combined_dfs.append(df_copy)
        
        self.combined_data = pd.concat(combined_dfs, ignore_index=True, sort=False)
        
        logger.info(f"Combined dataset created: {len(self.combined_data)} total records")
        
        # Perform initial data cleaning
        self.clean_data()
    
    def clean_data(self):
        """Comprehensive data cleaning and standardization"""
        logger.info("Starting data cleaning process...")
        
        if self.combined_data is None:
            raise ValueError("No combined data available. Please run create_combined_dataset() first.")
        
        # Standardize institution names
        self.combined_data['Institution_Name'] = self.combined_data['Institution_Name'].str.strip()
        
        # Handle missing values in categorical columns
        categorical_cols = ['Private/Government', 'Size', 'Focus', 'Research']
        for col in categorical_cols:
            if col in self.combined_data.columns:
                self.combined_data[col] = self.combined_data[col].fillna('Unknown')
        
        # Convert numeric columns
        numeric_cols = [col for col in self.combined_data.columns if 'Score' in col or 'Rank' in col]
        for col in numeric_cols:
            if col in self.combined_data.columns:
                self.combined_data[col] = pd.to_numeric(self.combined_data[col], errors='coerce')
        
        # Handle ranking columns (convert ranges like "601-650" to midpoint)
        rank_cols = [col for col in self.combined_data.columns if 'Rank' in col and col != 'Rank_2022']
        for col in rank_cols:
            if col in self.combined_data.columns:
                self.combined_data[col] = self.combined_data[col].apply(self.parse_ranking)
        
        logger.info("Data cleaning completed")
    
    def parse_ranking(self, rank_value):
        """
        Parse ranking values that might be ranges (e.g., "601-650")
        
        Parameters
        ----------
        rank_value : str or int
            Ranking value to parse
            
        Returns
        -------
        float
            Parsed ranking value (midpoint for ranges)
        """
        if pd.isna(rank_value):
            return np.nan
        
        if isinstance(rank_value, (int, float)):
            return float(rank_value)
        
        rank_str = str(rank_value).strip()
        
        # Handle ranges like "601-650"
        if '-' in rank_str:
            try:
                parts = rank_str.split('-')
                if len(parts) == 2:
                    start = float(parts[0])
                    end = float(parts[1])
                    return (start + end) / 2
            except ValueError:
                pass
        
        # Handle single numbers
        try:
            return float(rank_str)
        except ValueError:
            return np.nan
    
    def identify_symbiosis_data(self):
        """Identify and extract Symbiosis International University data"""
        logger.info("Identifying Symbiosis International University data...")
        
        if self.combined_data is None:
            raise ValueError("No combined data available.")
        
        # Search for Symbiosis variations
        symbiosis_patterns = [
            'Symbiosis International',
            'Symbiosis International University',
            'Symbiosis University',
            'Symbiosis'
        ]
        
        symbiosis_mask = pd.Series([False] * len(self.combined_data))
        
        for pattern in symbiosis_patterns:
            mask = self.combined_data['Institution_Name'].str.contains(pattern, case=False, na=False)
            symbiosis_mask = symbiosis_mask | mask
        
        self.symbiosis_data = self.combined_data[symbiosis_mask].copy()
        
        if len(self.symbiosis_data) > 0:
            logger.info(f"Found Symbiosis data for {len(self.symbiosis_data)} records across years")
            logger.info("Symbiosis entries found:")
            for _, row in self.symbiosis_data.iterrows():
                logger.info(f"  {row['Year']}: {row['Institution_Name']} (Rank: {row.get('Rank_' + str(row['Year']), 'N/A')})")
        else:
            logger.warning("No Symbiosis International University data found in the dataset")
        
        return self.symbiosis_data
    
    def identify_indian_institutions(self):
        """Identify and categorize all Indian institutions"""
        logger.info("Identifying Indian institutions...")
        
        if self.combined_data is None:
            raise ValueError("No combined data available.")
        
        # Filter for Indian institutions
        indian_mask = self.combined_data['Country'].str.contains('India', case=False, na=False)
        self.indian_institutions = self.combined_data[indian_mask].copy()
        
        logger.info(f"Found {len(self.indian_institutions)} Indian institution records")
        
        # Analyze by ownership type
        ownership_counts = self.indian_institutions['Private/Government'].value_counts()
        logger.info("Indian institutions by ownership:")
        for ownership, count in ownership_counts.items():
            logger.info(f"  {ownership}: {count}")
        
        return self.indian_institutions

    def analyze_symbiosis_performance(self):
        """Comprehensive analysis of Symbiosis performance over 5 years"""
        logger.info("Analyzing Symbiosis performance...")

        if self.symbiosis_data is None or len(self.symbiosis_data) == 0:
            logger.error("No Symbiosis data available for analysis")
            return None

        results = {
            'trajectory': {},
            'classification_changes': {},
            'metric_performance': {},
            'volatility_analysis': {}
        }

        # 5-Year trajectory analysis
        for year in self.years:
            year_data = self.symbiosis_data[self.symbiosis_data['Year'] == year]
            if len(year_data) > 0:
                row = year_data.iloc[0]
                rank_col = f'Rank_{year}'
                overall_rank = row.get(rank_col, np.nan)
                overall_score = row.get('Overall_Score', np.nan)

                results['trajectory'][year] = {
                    'rank': overall_rank,
                    'score': overall_score,
                    'classification': row.get('Focus', 'Unknown'),
                    'size': row.get('Size', 'Unknown'),
                    'research': row.get('Research', 'Unknown')
                }

        # Classification change analysis
        classifications = [results['trajectory'][year]['classification'] for year in self.years
                          if year in results['trajectory']]
        if len(set(classifications)) > 1:
            results['classification_changes'] = {
                'changes_detected': True,
                'timeline': {year: results['trajectory'][year]['classification']
                           for year in self.years if year in results['trajectory']}
            }

        # Metric-wise performance analysis
        metric_cols = [col for col in self.symbiosis_data.columns if 'Score' in col and col != 'Overall_Score']
        for metric in metric_cols:
            metric_data = {}
            for year in self.years:
                year_data = self.symbiosis_data[self.symbiosis_data['Year'] == year]
                if len(year_data) > 0 and metric in year_data.columns:
                    metric_data[year] = year_data.iloc[0][metric]
            results['metric_performance'][metric] = metric_data

        # Volatility analysis
        ranks = [results['trajectory'][year]['rank'] for year in self.years
                if year in results['trajectory'] and not pd.isna(results['trajectory'][year]['rank'])]
        if len(ranks) > 1:
            results['volatility_analysis'] = {
                'rank_std': np.std(ranks),
                'rank_range': max(ranks) - min(ranks),
                'trend': 'improving' if ranks[-1] < ranks[0] else 'declining' if ranks[-1] > ranks[0] else 'stable'
            }

        self.analysis_results['symbiosis_performance'] = results
        logger.info("Symbiosis performance analysis completed")
        return results

    def benchmark_private_indian_institutions(self):
        """Benchmark Symbiosis against private Indian institutions"""
        logger.info("Benchmarking against private Indian institutions...")

        if self.indian_institutions is None:
            logger.error("No Indian institutions data available")
            return None

        # Filter for private institutions
        private_indian = self.indian_institutions[
            self.indian_institutions['Private/Government'].str.contains('Private', case=False, na=False)
        ].copy()

        results = {
            'peer_comparison': {},
            'percentile_analysis': {},
            'performance_gaps': {}
        }

        # Year-wise comparison
        for year in self.years:
            year_private = private_indian[private_indian['Year'] == year]
            year_symbiosis = self.symbiosis_data[self.symbiosis_data['Year'] == year] if self.symbiosis_data is not None else pd.DataFrame()

            if len(year_private) > 0 and len(year_symbiosis) > 0:
                symbiosis_row = year_symbiosis.iloc[0]
                rank_col = f'Rank_{year}'
                symbiosis_rank = symbiosis_row.get(rank_col, np.nan)

                # Get ranks of all private Indian institutions
                private_ranks = year_private[rank_col].dropna()

                if not pd.isna(symbiosis_rank) and len(private_ranks) > 0:
                    # Calculate percentile position
                    better_count = len(private_ranks[private_ranks < symbiosis_rank])
                    percentile = (better_count / len(private_ranks)) * 100

                    results['peer_comparison'][year] = {
                        'symbiosis_rank': symbiosis_rank,
                        'total_private_indian': len(private_ranks),
                        'better_performers': better_count,
                        'percentile_position': percentile,
                        'median_private_rank': private_ranks.median(),
                        'best_private_rank': private_ranks.min()
                    }

        # Performance gap analysis
        metric_cols = [col for col in private_indian.columns if 'Score' in col]
        for metric in metric_cols:
            gap_analysis = {}
            for year in self.years:
                year_private = private_indian[private_indian['Year'] == year]
                year_symbiosis = self.symbiosis_data[self.symbiosis_data['Year'] == year] if self.symbiosis_data is not None else pd.DataFrame()

                if len(year_private) > 0 and len(year_symbiosis) > 0 and metric in year_private.columns:
                    private_scores = year_private[metric].dropna()
                    symbiosis_score = year_symbiosis.iloc[0].get(metric, np.nan)

                    if not pd.isna(symbiosis_score) and len(private_scores) > 0:
                        gap_analysis[year] = {
                            'symbiosis_score': symbiosis_score,
                            'private_median': private_scores.median(),
                            'private_mean': private_scores.mean(),
                            'private_max': private_scores.max(),
                            'gap_to_median': symbiosis_score - private_scores.median(),
                            'gap_to_best': symbiosis_score - private_scores.max()
                        }

            if gap_analysis:
                results['performance_gaps'][metric] = gap_analysis

        self.analysis_results['private_benchmarking'] = results
        logger.info("Private Indian institutions benchmarking completed")
        return results

    def analyze_superior_performers(self):
        """Analyze institutions ranking above Symbiosis"""
        logger.info("Analyzing superior performers...")

        if self.symbiosis_data is None or len(self.symbiosis_data) == 0:
            logger.error("No Symbiosis data available")
            return None

        results = {
            'superior_institutions': {},
            'performance_gaps': {},
            'common_characteristics': {}
        }

        # Get latest year Symbiosis rank
        latest_year = max(self.years)
        latest_symbiosis = self.symbiosis_data[self.symbiosis_data['Year'] == latest_year]

        if len(latest_symbiosis) == 0:
            logger.warning(f"No Symbiosis data for {latest_year}")
            return None

        rank_col = f'Rank_{latest_year}'
        symbiosis_rank = latest_symbiosis.iloc[0].get(rank_col, np.nan)

        if pd.isna(symbiosis_rank):
            logger.warning("Symbiosis rank not available for analysis")
            return None

        # Find all institutions ranking better than Symbiosis
        latest_data = self.combined_data[self.combined_data['Year'] == latest_year]
        superior_mask = latest_data[rank_col] < symbiosis_rank
        superior_institutions = latest_data[superior_mask].copy()

        results['superior_institutions'][latest_year] = {
            'count': len(superior_institutions),
            'symbiosis_rank': symbiosis_rank,
            'institutions': superior_institutions[['Institution_Name', 'Country', rank_col, 'Overall_Score']].to_dict('records')
        }

        # Analyze characteristics of superior performers
        if len(superior_institutions) > 0:
            characteristics = {
                'by_country': superior_institutions['Country'].value_counts().to_dict(),
                'by_ownership': superior_institutions['Private/Government'].value_counts().to_dict(),
                'by_size': superior_institutions['Size'].value_counts().to_dict(),
                'by_focus': superior_institutions['Focus'].value_counts().to_dict(),
                'by_research': superior_institutions['Research'].value_counts().to_dict()
            }
            results['common_characteristics'] = characteristics

        # Performance gap analysis
        metric_cols = [col for col in superior_institutions.columns if 'Score' in col]
        symbiosis_row = latest_symbiosis.iloc[0]

        for metric in metric_cols:
            if metric in superior_institutions.columns:
                superior_scores = superior_institutions[metric].dropna()
                symbiosis_score = symbiosis_row.get(metric, np.nan)

                if not pd.isna(symbiosis_score) and len(superior_scores) > 0:
                    results['performance_gaps'][metric] = {
                        'symbiosis_score': symbiosis_score,
                        'superior_median': superior_scores.median(),
                        'superior_mean': superior_scores.mean(),
                        'superior_max': superior_scores.max(),
                        'gap_to_median': superior_scores.median() - symbiosis_score,
                        'gap_to_mean': superior_scores.mean() - symbiosis_score,
                        'improvement_needed': superior_scores.quantile(0.25) - symbiosis_score
                    }

        self.analysis_results['superior_performers'] = results
        logger.info("Superior performers analysis completed")
        return results

    def analyze_indian_landscape(self):
        """Comprehensive analysis of Indian higher education landscape"""
        logger.info("Analyzing Indian higher education landscape...")

        if self.indian_institutions is None:
            logger.error("No Indian institutions data available")
            return None

        results = {
            'sector_trends': {},
            'rising_stars': {},
            'declining_performers': {},
            'regional_analysis': {}
        }

        # Sector-wise analysis (Government vs Private)
        for sector in ['Government', 'Private']:
            sector_data = self.indian_institutions[
                self.indian_institutions['Private/Government'].str.contains(sector, case=False, na=False)
            ]

            sector_trends = {}
            for year in self.years:
                year_data = sector_data[sector_data['Year'] == year]
                if len(year_data) > 0:
                    rank_col = f'Rank_{year}'
                    ranks = year_data[rank_col].dropna()
                    scores = year_data['Overall_Score'].dropna()

                    sector_trends[year] = {
                        'count': len(year_data),
                        'median_rank': ranks.median() if len(ranks) > 0 else np.nan,
                        'best_rank': ranks.min() if len(ranks) > 0 else np.nan,
                        'median_score': scores.median() if len(scores) > 0 else np.nan,
                        'top_performers': year_data.nsmallest(5, rank_col)[['Institution_Name', rank_col]].to_dict('records') if len(ranks) > 0 else []
                    }

            results['sector_trends'][sector] = sector_trends

        # Identify rising stars (institutions with significant improvement)
        institution_trajectories = {}
        for institution in self.indian_institutions['Institution_Name'].unique():
            inst_data = self.indian_institutions[self.indian_institutions['Institution_Name'] == institution]

            ranks = []
            years_with_data = []
            for year in self.years:
                year_data = inst_data[inst_data['Year'] == year]
                if len(year_data) > 0:
                    rank_col = f'Rank_{year}'
                    rank = year_data.iloc[0].get(rank_col, np.nan)
                    if not pd.isna(rank):
                        ranks.append(rank)
                        years_with_data.append(year)

            if len(ranks) >= 3:  # Need at least 3 years of data
                # Calculate improvement (lower rank is better)
                improvement = ranks[0] - ranks[-1]  # Positive means improvement
                improvement_rate = improvement / len(ranks)

                institution_trajectories[institution] = {
                    'years_tracked': len(ranks),
                    'first_rank': ranks[0],
                    'latest_rank': ranks[-1],
                    'improvement': improvement,
                    'improvement_rate': improvement_rate,
                    'trajectory': list(zip(years_with_data, ranks))
                }

        # Identify top rising stars
        rising_stars = sorted(
            [(inst, data) for inst, data in institution_trajectories.items() if data['improvement'] > 0],
            key=lambda x: x[1]['improvement_rate'],
            reverse=True
        )[:10]

        results['rising_stars'] = {
            'top_10': [{'institution': inst, **data} for inst, data in rising_stars],
            'criteria': 'Institutions with highest improvement rate over available years'
        }

        # Identify declining performers
        declining_performers = sorted(
            [(inst, data) for inst, data in institution_trajectories.items() if data['improvement'] < -50],
            key=lambda x: x[1]['improvement_rate']
        )[:10]

        results['declining_performers'] = {
            'top_10': [{'institution': inst, **data} for inst, data in declining_performers],
            'criteria': 'Institutions with significant ranking decline'
        }

        self.analysis_results['indian_landscape'] = results
        logger.info("Indian landscape analysis completed")
        return results

    def analyze_classification_transitions(self):
        """Analyze impact of institutional classification changes"""
        logger.info("Analyzing classification transitions...")

        if self.combined_data is None:
            logger.error("No combined data available")
            return None

        results = {
            'transition_patterns': {},
            'impact_analysis': {},
            'symbiosis_prediction': {}
        }

        # Track institutions that changed classifications
        institutions_with_changes = {}

        for institution in self.combined_data['Institution_Name'].unique():
            inst_data = self.combined_data[self.combined_data['Institution_Name'] == institution]
            classifications = inst_data['Focus'].dropna().unique()

            if len(classifications) > 1:
                # Institution changed classification
                timeline = {}
                for year in self.years:
                    year_data = inst_data[inst_data['Year'] == year]
                    if len(year_data) > 0:
                        focus = year_data.iloc[0].get('Focus', 'Unknown')
                        rank_col = f'Rank_{year}'
                        rank = year_data.iloc[0].get(rank_col, np.nan)
                        score = year_data.iloc[0].get('Overall_Score', np.nan)

                        timeline[year] = {
                            'classification': focus,
                            'rank': rank,
                            'score': score
                        }

                institutions_with_changes[institution] = timeline

        # Analyze transition impacts
        transition_impacts = {}
        for institution, timeline in institutions_with_changes.items():
            years_sorted = sorted(timeline.keys())

            for i in range(len(years_sorted) - 1):
                current_year = years_sorted[i]
                next_year = years_sorted[i + 1]

                current_class = timeline[current_year]['classification']
                next_class = timeline[next_year]['classification']

                if current_class != next_class:
                    transition_key = f"{current_class}_to_{next_class}"

                    if transition_key not in transition_impacts:
                        transition_impacts[transition_key] = []

                    current_rank = timeline[current_year]['rank']
                    next_rank = timeline[next_year]['rank']

                    if not pd.isna(current_rank) and not pd.isna(next_rank):
                        impact = current_rank - next_rank  # Positive means improvement
                        transition_impacts[transition_key].append({
                            'institution': institution,
                            'year_transition': f"{current_year}-{next_year}",
                            'rank_change': impact,
                            'before_rank': current_rank,
                            'after_rank': next_rank
                        })

        # Summarize transition impacts
        for transition, impacts in transition_impacts.items():
            if len(impacts) > 0:
                rank_changes = [impact['rank_change'] for impact in impacts]
                results['impact_analysis'][transition] = {
                    'sample_size': len(impacts),
                    'average_impact': np.mean(rank_changes),
                    'median_impact': np.median(rank_changes),
                    'positive_outcomes': len([x for x in rank_changes if x > 0]),
                    'negative_outcomes': len([x for x in rank_changes if x < 0]),
                    'examples': impacts[:3]  # Top 3 examples
                }

        # Predict Symbiosis CO to FC transition impact
        if 'CO_to_FC' in results['impact_analysis']:
            co_to_fc_data = results['impact_analysis']['CO_to_FC']
            results['symbiosis_prediction'] = {
                'transition_type': 'CO_to_FC',
                'historical_average_impact': co_to_fc_data['average_impact'],
                'success_probability': co_to_fc_data['positive_outcomes'] / co_to_fc_data['sample_size'],
                'recommendation': 'Favorable' if co_to_fc_data['average_impact'] > 0 else 'Cautionary'
            }

        self.analysis_results['classification_transitions'] = results
        logger.info("Classification transitions analysis completed")
        return results

    def analyze_metric_importance(self):
        """Analyze correlation between metrics and overall ranking"""
        logger.info("Analyzing metric importance...")

        if self.combined_data is None:
            logger.error("No combined data available")
            return None

        results = {
            'correlation_analysis': {},
            'metric_weights': {},
            'improvement_priorities': {}
        }

        # Get latest year data for correlation analysis
        latest_year = max(self.years)
        latest_data = self.combined_data[self.combined_data['Year'] == latest_year].copy()

        # Identify metric columns
        metric_cols = [col for col in latest_data.columns if 'Score' in col and col != 'Overall_Score']
        rank_col = f'Rank_{latest_year}'

        # Calculate correlations
        correlations = {}
        for metric in metric_cols:
            if metric in latest_data.columns and rank_col in latest_data.columns:
                # Remove missing values
                clean_data = latest_data[[metric, rank_col]].dropna()
                if len(clean_data) > 10:  # Need sufficient data
                    # Correlation with rank (negative correlation means higher score = better rank)
                    corr_rank = clean_data[metric].corr(clean_data[rank_col])

                    # Correlation with overall score
                    if 'Overall_Score' in latest_data.columns:
                        clean_score_data = latest_data[[metric, 'Overall_Score']].dropna()
                        if len(clean_score_data) > 10:
                            corr_score = clean_score_data[metric].corr(clean_score_data['Overall_Score'])
                        else:
                            corr_score = np.nan
                    else:
                        corr_score = np.nan

                    correlations[metric] = {
                        'rank_correlation': corr_rank,
                        'score_correlation': corr_score,
                        'importance_score': abs(corr_rank) if not pd.isna(corr_rank) else 0
                    }

        results['correlation_analysis'] = correlations

        # Rank metrics by importance
        metric_importance = sorted(
            [(metric, data['importance_score']) for metric, data in correlations.items()],
            key=lambda x: x[1],
            reverse=True
        )

        results['metric_weights'] = {
            'ranking': metric_importance,
            'top_5_metrics': metric_importance[:5]
        }

        # Analyze Symbiosis improvement priorities
        if self.symbiosis_data is not None and len(self.symbiosis_data) > 0:
            latest_symbiosis = self.symbiosis_data[self.symbiosis_data['Year'] == latest_year]
            if len(latest_symbiosis) > 0:
                symbiosis_row = latest_symbiosis.iloc[0]

                improvement_priorities = []
                for metric, importance in metric_importance[:10]:  # Top 10 metrics
                    symbiosis_score = symbiosis_row.get(metric, np.nan)
                    if not pd.isna(symbiosis_score):
                        # Compare with top performers
                        metric_data = latest_data[metric].dropna()
                        percentile = stats.percentileofscore(metric_data, symbiosis_score)
                        gap_to_top10 = metric_data.quantile(0.9) - symbiosis_score

                        improvement_priorities.append({
                            'metric': metric,
                            'importance_score': importance,
                            'current_score': symbiosis_score,
                            'percentile_position': percentile,
                            'gap_to_top10': gap_to_top10,
                            'improvement_potential': importance * max(0, gap_to_top10)
                        })

                # Sort by improvement potential
                improvement_priorities.sort(key=lambda x: x['improvement_potential'], reverse=True)
                results['improvement_priorities'] = improvement_priorities

        self.analysis_results['metric_importance'] = results
        logger.info("Metric importance analysis completed")
        return results

    def predictive_modeling(self):
        """Perform predictive modeling for ranking forecasts"""
        logger.info("Performing predictive modeling...")

        if self.symbiosis_data is None or len(self.symbiosis_data) == 0:
            logger.error("No Symbiosis data available for prediction")
            return None

        results = {
            'ranking_forecast': {},
            'scenario_analysis': {},
            'confidence_intervals': {}
        }

        # Prepare Symbiosis time series data
        symbiosis_trajectory = []
        years_with_data = []

        for year in sorted(self.years):
            year_data = self.symbiosis_data[self.symbiosis_data['Year'] == year]
            if len(year_data) > 0:
                rank_col = f'Rank_{year}'
                rank = year_data.iloc[0].get(rank_col, np.nan)
                if not pd.isna(rank):
                    symbiosis_trajectory.append(rank)
                    years_with_data.append(year)

        if len(symbiosis_trajectory) >= 3:
            # Simple linear regression for trend
            X = np.array(range(len(symbiosis_trajectory))).reshape(-1, 1)
            y = np.array(symbiosis_trajectory)

            model = LinearRegression()
            model.fit(X, y)

            # Forecast next 3 years
            future_X = np.array(range(len(symbiosis_trajectory), len(symbiosis_trajectory) + 3)).reshape(-1, 1)
            future_predictions = model.predict(future_X)

            # Calculate confidence intervals (simple approach)
            residuals = y - model.predict(X)
            mse = np.mean(residuals ** 2)
            std_error = np.sqrt(mse)

            forecast_years = [max(self.years) + i + 1 for i in range(3)]

            results['ranking_forecast'] = {
                'model_r2': r2_score(y, model.predict(X)),
                'trend_slope': model.coef_[0],
                'forecasts': [
                    {
                        'year': year,
                        'predicted_rank': pred,
                        'confidence_lower': pred - 1.96 * std_error,
                        'confidence_upper': pred + 1.96 * std_error
                    }
                    for year, pred in zip(forecast_years, future_predictions)
                ]
            }

            # Scenario analysis
            scenarios = {
                'conservative': [pred + std_error for pred in future_predictions],
                'optimistic': [pred - std_error for pred in future_predictions],
                'current_trend': future_predictions.tolist()
            }

            results['scenario_analysis'] = {
                'scenarios': scenarios,
                'forecast_years': forecast_years
            }

        self.analysis_results['predictive_modeling'] = results
        logger.info("Predictive modeling completed")
        return results

    def analyze_global_patterns(self):
        """Analyze global classification and performance patterns"""
        logger.info("Analyzing global patterns...")

        if self.combined_data is None:
            logger.error("No combined data available")
            return None

        results = {
            'classification_distribution': {},
            'global_vs_india': {},
            'success_patterns': {}
        }

        # Global classification distribution by year
        for year in self.years:
            year_data = self.combined_data[self.combined_data['Year'] == year]

            global_dist = year_data['Focus'].value_counts().to_dict()

            # India-specific distribution
            india_data = year_data[year_data['Country'].str.contains('India', case=False, na=False)]
            india_dist = india_data['Focus'].value_counts().to_dict()

            results['classification_distribution'][year] = {
                'global': global_dist,
                'india': india_dist,
                'india_percentage': {
                    focus: (count / global_dist.get(focus, 1)) * 100
                    for focus, count in india_dist.items()
                }
            }

        # Compare India vs Global performance
        latest_year = max(self.years)
        latest_data = self.combined_data[self.combined_data['Year'] == latest_year]

        # Global statistics
        rank_col = f'Rank_{latest_year}'
        global_ranks = latest_data[rank_col].dropna()
        global_scores = latest_data['Overall_Score'].dropna()

        # India statistics
        india_data = latest_data[latest_data['Country'].str.contains('India', case=False, na=False)]
        india_ranks = india_data[rank_col].dropna()
        india_scores = india_data['Overall_Score'].dropna()

        results['global_vs_india'] = {
            'global_stats': {
                'median_rank': global_ranks.median(),
                'median_score': global_scores.median(),
                'top_100_count': len(global_ranks[global_ranks <= 100])
            },
            'india_stats': {
                'median_rank': india_ranks.median(),
                'median_score': india_scores.median(),
                'top_100_count': len(india_ranks[india_ranks <= 100]),
                'total_institutions': len(india_data)
            },
            'india_global_position': {
                'rank_percentile': stats.percentileofscore(global_ranks, india_ranks.median()) if len(india_ranks) > 0 else 0,
                'score_percentile': stats.percentileofscore(global_scores, india_scores.median()) if len(india_scores) > 0 else 0
            }
        }

        # Identify success patterns among top performers
        top_100 = latest_data[latest_data[rank_col] <= 100]
        if len(top_100) > 0:
            success_patterns = {
                'by_classification': top_100['Focus'].value_counts(normalize=True).to_dict(),
                'by_size': top_100['Size'].value_counts(normalize=True).to_dict(),
                'by_research': top_100['Research'].value_counts(normalize=True).to_dict(),
                'by_ownership': top_100['Private/Government'].value_counts(normalize=True).to_dict(),
                'top_countries': top_100['Country'].value_counts().head(10).to_dict()
            }
            results['success_patterns'] = success_patterns

        self.analysis_results['global_patterns'] = results
        logger.info("Global patterns analysis completed")
        return results

    def create_visualizations(self):
        """Create comprehensive visualizations for all analyses"""
        logger.info("Creating visualizations...")

        if not self.analysis_results:
            logger.error("No analysis results available for visualization")
            return None

        # Create output directory for figures
        output_dir = Path("output/figures")
        output_dir.mkdir(parents=True, exist_ok=True)

        visualizations = {}

        # 1. Symbiosis Performance Trajectory
        if 'symbiosis_performance' in self.analysis_results:
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle('Symbiosis International University - Performance Analysis (2022-2026)',
                        fontsize=16, fontweight='bold')

            trajectory = self.analysis_results['symbiosis_performance']['trajectory']

            # Ranking trajectory
            years = list(trajectory.keys())
            ranks = [trajectory[year]['rank'] for year in years if not pd.isna(trajectory[year]['rank'])]
            scores = [trajectory[year]['score'] for year in years if not pd.isna(trajectory[year]['score'])]

            if len(ranks) > 0:
                axes[0, 0].plot(years[:len(ranks)], ranks, marker='o', linewidth=2, markersize=8,
                               color=self.COLOR_PALETTE['symbiosis'])
                axes[0, 0].set_title('Overall Ranking Trend')
                axes[0, 0].set_xlabel('Year')
                axes[0, 0].set_ylabel('QS Ranking')
                axes[0, 0].invert_yaxis()  # Lower rank is better
                axes[0, 0].grid(True, alpha=0.3)

            # Score trajectory
            if len(scores) > 0:
                axes[0, 1].plot(years[:len(scores)], scores, marker='s', linewidth=2, markersize=8,
                               color=self.COLOR_PALETTE['highlight'][3])
                axes[0, 1].set_title('Overall Score Trend')
                axes[0, 1].set_xlabel('Year')
                axes[0, 1].set_ylabel('QS Score')
                axes[0, 1].grid(True, alpha=0.3)

            # Classification changes
            classifications = [trajectory[year]['classification'] for year in years]
            unique_classes = list(set(classifications))
            class_colors = dict(zip(unique_classes, self.COLOR_PALETTE['categorical'][:len(unique_classes)]))

            for i, (year, class_type) in enumerate(zip(years, classifications)):
                axes[1, 0].bar(i, 1, color=class_colors[class_type], alpha=0.7)

            axes[1, 0].set_title('Classification Changes Over Time')
            axes[1, 0].set_xlabel('Year')
            axes[1, 0].set_xticks(range(len(years)))
            axes[1, 0].set_xticklabels(years)
            axes[1, 0].set_ylabel('Classification')

            # Create legend for classifications
            legend_elements = [plt.Rectangle((0,0),1,1, facecolor=class_colors[class_type], alpha=0.7)
                             for class_type in unique_classes]
            axes[1, 0].legend(legend_elements, unique_classes, loc='upper right')

            # Metric performance radar chart (if available)
            if 'metric_performance' in self.analysis_results['symbiosis_performance']:
                metric_data = self.analysis_results['symbiosis_performance']['metric_performance']
                latest_year = max(self.years)

                metrics = []
                values = []
                for metric, year_data in metric_data.items():
                    if latest_year in year_data and not pd.isna(year_data[latest_year]):
                        metric_name = metric.replace('_Score', '').replace('_', ' ')
                        metrics.append(metric_name)
                        values.append(year_data[latest_year])

                if len(metrics) > 0:
                    # Simple bar chart instead of radar for now
                    axes[1, 1].barh(metrics, values, color=self.COLOR_PALETTE['sequential'][0])
                    axes[1, 1].set_title(f'Metric Scores ({latest_year})')
                    axes[1, 1].set_xlabel('Score')

            plt.tight_layout()
            plt.savefig(output_dir / 'symbiosis_performance_analysis.png', dpi=300, bbox_inches='tight')
            plt.close()

            visualizations['symbiosis_performance'] = str(output_dir / 'symbiosis_performance_analysis.png')

        # 2. Private Indian Institutions Benchmarking
        if 'private_benchmarking' in self.analysis_results:
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle('Symbiosis vs Private Indian Institutions Benchmarking',
                        fontsize=16, fontweight='bold')

            peer_comparison = self.analysis_results['private_benchmarking']['peer_comparison']

            # Percentile position over time
            years = list(peer_comparison.keys())
            percentiles = [peer_comparison[year]['percentile_position'] for year in years]

            axes[0, 0].plot(years, percentiles, marker='o', linewidth=2, markersize=8,
                           color=self.COLOR_PALETTE['symbiosis'])
            axes[0, 0].set_title('Percentile Position Among Private Indian Institutions')
            axes[0, 0].set_xlabel('Year')
            axes[0, 0].set_ylabel('Percentile Position')
            axes[0, 0].set_ylim(0, 100)
            axes[0, 0].grid(True, alpha=0.3)

            # Ranking comparison
            symbiosis_ranks = [peer_comparison[year]['symbiosis_rank'] for year in years]
            median_ranks = [peer_comparison[year]['median_private_rank'] for year in years]

            axes[0, 1].plot(years, symbiosis_ranks, marker='o', linewidth=2, label='Symbiosis',
                           color=self.COLOR_PALETTE['symbiosis'])
            axes[0, 1].plot(years, median_ranks, marker='s', linewidth=2, label='Private Indian Median',
                           color=self.COLOR_PALETTE['private'])
            axes[0, 1].set_title('Ranking Comparison')
            axes[0, 1].set_xlabel('Year')
            axes[0, 1].set_ylabel('QS Ranking')
            axes[0, 1].invert_yaxis()
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)

            # Performance gaps (latest year)
            if 'performance_gaps' in self.analysis_results['private_benchmarking']:
                gaps_data = self.analysis_results['private_benchmarking']['performance_gaps']
                latest_year = max(self.years)

                metrics = []
                gaps_to_median = []

                for metric, year_data in gaps_data.items():
                    if latest_year in year_data:
                        metric_name = metric.replace('_Score', '').replace('_', ' ')
                        metrics.append(metric_name)
                        gaps_to_median.append(year_data[latest_year]['gap_to_median'])

                if len(metrics) > 0:
                    colors = ['green' if gap >= 0 else 'red' for gap in gaps_to_median]
                    axes[1, 0].barh(metrics, gaps_to_median, color=colors, alpha=0.7)
                    axes[1, 0].set_title(f'Performance Gaps vs Private Indian Median ({latest_year})')
                    axes[1, 0].set_xlabel('Score Difference')
                    axes[1, 0].axvline(x=0, color='black', linestyle='--', alpha=0.5)

            plt.tight_layout()
            plt.savefig(output_dir / 'private_benchmarking.png', dpi=300, bbox_inches='tight')
            plt.close()

            visualizations['private_benchmarking'] = str(output_dir / 'private_benchmarking.png')

        # 3. Indian Higher Education Landscape
        if 'indian_landscape' in self.analysis_results:
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle('Indian Higher Education Landscape Analysis',
                        fontsize=16, fontweight='bold')

            sector_trends = self.analysis_results['indian_landscape']['sector_trends']

            # Government vs Private trends
            years = self.years
            gov_medians = []
            private_medians = []

            for year in years:
                if 'Government' in sector_trends and year in sector_trends['Government']:
                    gov_medians.append(sector_trends['Government'][year]['median_rank'])
                else:
                    gov_medians.append(np.nan)

                if 'Private' in sector_trends and year in sector_trends['Private']:
                    private_medians.append(sector_trends['Private'][year]['median_rank'])
                else:
                    private_medians.append(np.nan)

            axes[0, 0].plot(years, gov_medians, marker='o', linewidth=2, label='Government',
                           color=self.COLOR_PALETTE['government'])
            axes[0, 0].plot(years, private_medians, marker='s', linewidth=2, label='Private',
                           color=self.COLOR_PALETTE['private'])
            axes[0, 0].set_title('Median Rankings: Government vs Private')
            axes[0, 0].set_xlabel('Year')
            axes[0, 0].set_ylabel('Median QS Ranking')
            axes[0, 0].invert_yaxis()
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)

            # Rising stars
            if 'rising_stars' in self.analysis_results['indian_landscape']:
                rising_stars = self.analysis_results['indian_landscape']['rising_stars']['top_10']

                if len(rising_stars) > 0:
                    institutions = [star['institution'][:30] + '...' if len(star['institution']) > 30
                                  else star['institution'] for star in rising_stars[:10]]
                    improvements = [star['improvement'] for star in rising_stars[:10]]

                    axes[0, 1].barh(institutions, improvements, color=self.COLOR_PALETTE['sequential'][2])
                    axes[0, 1].set_title('Top Rising Stars (Ranking Improvement)')
                    axes[0, 1].set_xlabel('Ranking Improvement')

            plt.tight_layout()
            plt.savefig(output_dir / 'indian_landscape.png', dpi=300, bbox_inches='tight')
            plt.close()

            visualizations['indian_landscape'] = str(output_dir / 'indian_landscape.png')

        logger.info(f"Created {len(visualizations)} visualizations")
        return visualizations

    def run_comprehensive_analysis(self):
        """Execute the complete analysis pipeline"""
        logger.info("Starting comprehensive QS WUR analysis...")

        try:
            # Phase 1: Data Foundation
            logger.info("Phase 1: Data Foundation & Preprocessing")
            self.load_all_data()
            self.identify_symbiosis_data()
            self.identify_indian_institutions()

            # Phase 2: Core Analysis
            logger.info("Phase 2: Core Performance Analysis")
            self.analyze_symbiosis_performance()
            self.benchmark_private_indian_institutions()
            self.analyze_superior_performers()

            # Phase 3: Competitive Intelligence
            logger.info("Phase 3: Competitive Intelligence")
            self.analyze_indian_landscape()
            self.analyze_classification_transitions()

            # Phase 4: Strategic Analytics
            logger.info("Phase 4: Strategic Analytics")
            self.analyze_metric_importance()
            self.predictive_modeling()

            # Phase 5: Global Context
            logger.info("Phase 5: Global Context Analysis")
            self.analyze_global_patterns()

            # Phase 6: Visualizations
            logger.info("Phase 6: Creating Visualizations")
            self.create_visualizations()

            logger.info("Comprehensive analysis completed successfully!")
            return self.analysis_results

        except Exception as e:
            logger.error(f"Error during analysis: {str(e)}")
            raise


def main():
    """Main execution function"""
    print("QS World University Rankings Comprehensive Analysis")
    print("=" * 60)

    # Initialize analyzer
    analyzer = QSWURAnalyzer()

    # Run comprehensive analysis
    results = analyzer.run_comprehensive_analysis()

    # Print summary
    print("\nAnalysis Summary:")
    print("-" * 30)
    for analysis_type, result in results.items():
        print(f"✓ {analysis_type.replace('_', ' ').title()}: Completed")

    print(f"\nTotal analysis components: {len(results)}")
    print("Analysis results saved to analyzer.analysis_results")

    return analyzer


if __name__ == "__main__":
    analyzer = main()
