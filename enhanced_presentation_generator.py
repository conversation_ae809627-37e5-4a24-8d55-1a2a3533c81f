"""
Enhanced PowerPoint Presentation Generator
Author: Dr. <PERSON><PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-20
Description: Create comprehensive board-ready PowerPoint presentation with detailed analysis
"""

import pandas as pd
import numpy as np
from pathlib import Path
import logging
from datetime import datetime
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
import matplotlib.pyplot as plt
import seaborn as sns
from io import BytesIO

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedPresentationGenerator:
    """
    Enhanced PowerPoint presentation generator for QS WUR analysis
    Creates comprehensive board-ready presentation with detailed insights
    """
    
    def __init__(self, analysis_results: dict, output_directory: str = "output"):
        """Initialize the enhanced presentation generator"""
        self.analysis_results = analysis_results
        self.output_dir = Path(output_directory)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories
        (self.output_dir / "reports").mkdir(exist_ok=True)
        (self.output_dir / "figures").mkdir(exist_ok=True)
        
        self.report_date = datetime.now().strftime("%Y-%m-%d")
        
        # Color scheme for professional presentation
        self.colors = {
            'primary': RGBColor(31, 119, 180),      # Blue
            'secondary': RGBColor(255, 127, 14),     # Orange
            'success': RGBColor(44, 160, 44),        # Green
            'warning': RGBColor(214, 39, 40),        # Red
            'info': RGBColor(148, 103, 189),         # Purple
            'dark': RGBColor(64, 64, 64),            # Dark Gray
            'light': RGBColor(127, 127, 127)         # Light Gray
        }
        
        logger.info("Enhanced Presentation Generator initialized")
    
    def create_enhanced_presentation(self):
        """Create comprehensive board-ready PowerPoint presentation"""
        logger.info("Creating enhanced PowerPoint presentation...")
        
        # Create new presentation
        prs = Presentation()
        
        # Title slide
        self.add_enhanced_title_slide(prs)
        
        # Executive summary section
        self.add_executive_summary_section(prs)
        
        # Symbiosis performance section
        self.add_symbiosis_performance_section(prs)
        
        # Competitive analysis section
        self.add_competitive_analysis_section(prs)
        
        # Strategic insights section
        self.add_strategic_insights_section(prs)
        
        # Recommendations and conclusions
        self.add_recommendations_section(prs)
        
        # Appendix
        self.add_appendix_section(prs)
        
        # Save presentation
        ppt_path = self.output_dir / "reports" / f"QS_WUR_Enhanced_Presentation_{self.report_date}.pptx"
        prs.save(ppt_path)
        
        logger.info(f"Enhanced PowerPoint presentation saved: {ppt_path}")
        return ppt_path
    
    def add_enhanced_title_slide(self, prs):
        """Add enhanced title slide"""
        slide_layout = prs.slide_layouts[0]  # Title slide layout
        slide = prs.slides.add_slide(slide_layout)
        
        title = slide.shapes.title
        subtitle = slide.placeholders[1]
        
        title.text = "QS World University Rankings\nStrategic Analysis Report"
        
        # Format title
        title_paragraph = title.text_frame.paragraphs[0]
        title_paragraph.font.size = Pt(36)
        title_paragraph.font.bold = True
        title_paragraph.font.color.rgb = self.colors['primary']
        
        subtitle.text = (
            "Comprehensive Performance Analysis for Symbiosis International University\n"
            "Five-Year Trend Analysis (2022-2026)\n\n"
            "Prepared for: Board of Directors\n"
            f"Date: {self.report_date}\n\n"
            "Dr. Dharmendra Pandey\n"
            "Deputy Director - Quality Management & Benchmarking\n"
            "Head - Quality Assurance"
        )
        
        # Format subtitle
        for paragraph in subtitle.text_frame.paragraphs:
            paragraph.font.size = Pt(14)
            paragraph.alignment = PP_ALIGN.CENTER
    
    def add_executive_summary_section(self, prs):
        """Add executive summary section with multiple slides"""
        
        # Agenda slide
        slide_layout = prs.slide_layouts[1]  # Title and content
        slide = prs.slides.add_slide(slide_layout)
        
        title = slide.shapes.title
        content = slide.placeholders[1]
        
        title.text = "Presentation Agenda"
        
        agenda_items = [
            "1. Executive Summary & Key Findings",
            "2. Symbiosis Performance Analysis (2025-2026)",
            "3. Competitive Benchmarking Analysis",
            "4. Indian Higher Education Landscape",
            "5. Global Context & Classification Trends",
            "6. Strategic Insights & Performance Drivers",
            "7. Predictive Analysis & Future Scenarios",
            "8. Data-Driven Observations & Conclusions"
        ]
        
        content.text = "\n".join(agenda_items)
        
        # Format content
        for paragraph in content.text_frame.paragraphs:
            paragraph.font.size = Pt(18)
            paragraph.space_after = Pt(12)
        
        # Key findings slide
        slide = prs.slides.add_slide(slide_layout)
        title = slide.shapes.title
        content = slide.placeholders[1]
        
        title.text = "Executive Summary - Key Findings"
        
        if 'symbiosis_performance' in self.analysis_results:
            trajectory = self.analysis_results['symbiosis_performance']['trajectory']
            
            key_findings = [
                "✓ Symbiosis International University maintains global QS ranking presence",
                "✓ Current ranking: 696 (2026), representing a decline from 641 (2025)",
                "✓ Consistent institutional classification throughout analysis period",
                "✓ Competitive positioning within private Indian institution cohort identified",
                "✓ Clear performance improvement opportunities documented",
                "✓ Strategic focus areas prioritized based on correlation analysis",
                "✓ Predictive modeling indicates potential for ranking recovery"
            ]
            
            content.text = "\n".join(key_findings)
            
            # Format with colors
            for i, paragraph in enumerate(content.text_frame.paragraphs):
                paragraph.font.size = Pt(16)
                paragraph.space_after = Pt(8)
                if i < len(key_findings):
                    paragraph.font.color.rgb = self.colors['success']
    
    def add_symbiosis_performance_section(self, prs):
        """Add detailed Symbiosis performance analysis slides"""
        
        # Performance trajectory slide
        slide_layout = prs.slide_layouts[1]
        slide = prs.slides.add_slide(slide_layout)
        
        title = slide.shapes.title
        content = slide.placeholders[1]
        
        title.text = "Symbiosis Performance Trajectory (2025-2026)"
        
        if 'symbiosis_performance' in self.analysis_results:
            trajectory = self.analysis_results['symbiosis_performance']['trajectory']
            
            performance_details = []
            for year, data in trajectory.items():
                rank = data.get('rank', 'N/A')
                score = data.get('score', 'N/A')
                classification = data.get('classification', 'N/A')
                size = data.get('size', 'N/A')
                research = data.get('research', 'N/A')
                
                performance_details.append(
                    f"📊 {year}: Rank {rank} | Score: {score} | "
                    f"Classification: {classification} | Size: {size} | Research: {research}"
                )
            
            # Add volatility analysis
            if 'volatility_analysis' in self.analysis_results['symbiosis_performance']:
                volatility = self.analysis_results['symbiosis_performance']['volatility_analysis']
                performance_details.extend([
                    "",
                    "📈 Ranking Stability Analysis:",
                    f"   • Standard Deviation: {volatility.get('rank_std', 'N/A'):.1f}",
                    f"   • Ranking Range: {volatility.get('rank_range', 'N/A')} positions",
                    f"   • Overall Trend: {volatility.get('trend', 'N/A').title()}"
                ])
            
            content.text = "\n".join(performance_details)
            
            # Format content
            for paragraph in content.text_frame.paragraphs:
                paragraph.font.size = Pt(14)
                paragraph.space_after = Pt(6)
        
        # Private benchmarking slide
        slide = prs.slides.add_slide(slide_layout)
        title = slide.shapes.title
        content = slide.placeholders[1]
        
        title.text = "Benchmarking vs Private Indian Institutions"
        
        if 'private_benchmarking' in self.analysis_results:
            benchmark_data = self.analysis_results['private_benchmarking']
            
            benchmark_details = ["🎯 Competitive Position Analysis:", ""]
            
            if 'peer_comparison' in benchmark_data:
                for year, data in benchmark_data['peer_comparison'].items():
                    symbiosis_rank = data.get('symbiosis_rank', 'N/A')
                    total_private = data.get('total_private_indian', 'N/A')
                    percentile = data.get('percentile_position', 'N/A')
                    median_rank = data.get('median_private_rank', 'N/A')
                    
                    benchmark_details.extend([
                        f"📅 {year} Performance:",
                        f"   • Symbiosis Rank: {symbiosis_rank}",
                        f"   • Total Private Indian Institutions: {total_private}",
                        f"   • Percentile Position: {percentile:.1f}%",
                        f"   • Private Indian Median Rank: {median_rank}",
                        ""
                    ])
            
            content.text = "\n".join(benchmark_details)
            
            # Format content
            for paragraph in content.text_frame.paragraphs:
                paragraph.font.size = Pt(14)
                paragraph.space_after = Pt(4)
    
    def add_competitive_analysis_section(self, prs):
        """Add competitive analysis slides"""
        
        # Superior performers slide
        slide_layout = prs.slide_layouts[1]
        slide = prs.slides.add_slide(slide_layout)
        
        title = slide.shapes.title
        content = slide.placeholders[1]
        
        title.text = "Superior Performers Analysis"
        
        if 'superior_performers' in self.analysis_results:
            superior_data = self.analysis_results['superior_performers']
            
            analysis_details = ["🏆 Institutions Ranking Above Symbiosis (2026):", ""]
            
            if 'superior_institutions' in superior_data:
                latest_year = max(superior_data['superior_institutions'].keys())
                superior_info = superior_data['superior_institutions'][latest_year]
                
                analysis_details.extend([
                    f"📊 Total Superior Performers: {superior_info.get('count', 'N/A')}",
                    f"📍 Symbiosis Current Rank: {superior_info.get('symbiosis_rank', 'N/A')}",
                    ""
                ])
            
            # Add characteristics analysis
            if 'common_characteristics' in superior_data:
                characteristics = superior_data['common_characteristics']
                analysis_details.append("🔍 Common Characteristics of Superior Performers:")
                
                if 'by_country' in characteristics:
                    top_countries = list(characteristics['by_country'].items())[:5]
                    analysis_details.append("   Top Countries:")
                    for country, count in top_countries:
                        analysis_details.append(f"     • {country}: {count} institutions")
                
                analysis_details.append("")
                
                if 'by_focus' in characteristics:
                    focus_dist = characteristics['by_focus']
                    analysis_details.append("   By Classification:")
                    for focus, count in focus_dist.items():
                        analysis_details.append(f"     • {focus}: {count} institutions")
            
            content.text = "\n".join(analysis_details)
            
            # Format content
            for paragraph in content.text_frame.paragraphs:
                paragraph.font.size = Pt(13)
                paragraph.space_after = Pt(4)
        
        # Indian landscape slide
        slide = prs.slides.add_slide(slide_layout)
        title = slide.shapes.title
        content = slide.placeholders[1]
        
        title.text = "Indian Higher Education Landscape"
        
        if 'indian_landscape' in self.analysis_results:
            landscape_data = self.analysis_results['indian_landscape']
            
            landscape_details = ["🇮🇳 Indian Higher Education Overview:", ""]
            
            # Sector trends
            if 'sector_trends' in landscape_data:
                sector_trends = landscape_data['sector_trends']
                
                for sector, trends in sector_trends.items():
                    latest_year = max(trends.keys()) if trends else None
                    if latest_year and latest_year in trends:
                        data = trends[latest_year]
                        landscape_details.extend([
                            f"🏛️ {sector} Institutions ({latest_year}):",
                            f"   • Total Count: {data.get('count', 'N/A')}",
                            f"   • Median Rank: {data.get('median_rank', 'N/A')}",
                            f"   • Best Rank: {data.get('best_rank', 'N/A')}",
                            ""
                        ])
            
            # Rising stars
            if 'rising_stars' in landscape_data:
                rising_stars = landscape_data['rising_stars']['top_10']
                if rising_stars:
                    landscape_details.extend([
                        "⭐ Top Rising Star Institutions:",
                        ""
                    ])
                    
                    for i, star in enumerate(rising_stars[:5], 1):
                        institution = star['institution']
                        improvement = star['improvement']
                        landscape_details.append(f"   {i}. {institution}: +{improvement:.0f} positions")
            
            content.text = "\n".join(landscape_details)
            
            # Format content
            for paragraph in content.text_frame.paragraphs:
                paragraph.font.size = Pt(13)
                paragraph.space_after = Pt(4)

    def add_strategic_insights_section(self, prs):
        """Add strategic insights and analysis slides"""

        # Global context slide
        slide_layout = prs.slide_layouts[1]
        slide = prs.slides.add_slide(slide_layout)

        title = slide.shapes.title
        content = slide.placeholders[1]

        title.text = "Global Context & Classification Analysis"

        if 'global_patterns' in self.analysis_results:
            global_data = self.analysis_results['global_patterns']

            global_details = ["🌍 Global Higher Education Context:", ""]

            if 'global_vs_india' in global_data:
                comparison = global_data['global_vs_india']

                global_details.extend([
                    "📊 Global vs Indian Performance:",
                    f"   • Global Median Rank: {comparison['global_stats'].get('median_rank', 'N/A')}",
                    f"   • Indian Median Rank: {comparison['india_stats'].get('median_rank', 'N/A')}",
                    f"   • Indian Institutions in Top 100: {comparison['india_stats'].get('top_100_count', 'N/A')}",
                    f"   • Total Indian Institutions: {comparison['india_stats'].get('total_institutions', 'N/A')}",
                    ""
                ])

            # Classification distribution
            if 'classification_distribution' in global_data:
                latest_year = max(global_data['classification_distribution'].keys())
                latest_dist = global_data['classification_distribution'][latest_year]

                global_details.extend([
                    f"🏛️ Classification Distribution ({latest_year}):",
                    ""
                ])

                if 'india' in latest_dist:
                    global_details.append("   Indian Institutions:")
                    for classification, count in latest_dist['india'].items():
                        global_details.append(f"     • {classification}: {count}")

            content.text = "\n".join(global_details)

            # Format content
            for paragraph in content.text_frame.paragraphs:
                paragraph.font.size = Pt(14)
                paragraph.space_after = Pt(4)

        # Metric importance slide
        slide = prs.slides.add_slide(slide_layout)
        title = slide.shapes.title
        content = slide.placeholders[1]

        title.text = "Performance Driver Analysis"

        if 'metric_importance' in self.analysis_results:
            metric_data = self.analysis_results['metric_importance']

            metric_details = ["📈 Key Performance Drivers:", ""]

            if 'metric_weights' in metric_data and 'top_5_metrics' in metric_data['metric_weights']:
                top_metrics = metric_data['metric_weights']['top_5_metrics']

                metric_details.append("🎯 Top 5 Metrics (Highest Correlation with Overall Ranking):")
                metric_details.append("")

                for i, (metric, importance) in enumerate(top_metrics, 1):
                    metric_name = metric.replace('_Score', '').replace('_', ' ').title()
                    metric_details.append(f"   {i}. {metric_name}: {importance:.3f}")

                metric_details.append("")

            # Improvement priorities for Symbiosis
            if 'improvement_priorities' in metric_data:
                priorities = metric_data['improvement_priorities']

                if priorities:
                    metric_details.extend([
                        "🎯 Priority Areas for Symbiosis:",
                        ""
                    ])

                    for i, priority in enumerate(priorities[:5], 1):
                        metric_name = priority['metric'].replace('_Score', '').replace('_', ' ').title()
                        current_score = priority['current_score']
                        percentile = priority['percentile_position']

                        metric_details.extend([
                            f"   {i}. {metric_name}",
                            f"      Current Score: {current_score:.1f} | Percentile: {percentile:.1f}%",
                            ""
                        ])

            content.text = "\n".join(metric_details)

            # Format content
            for paragraph in content.text_frame.paragraphs:
                paragraph.font.size = Pt(13)
                paragraph.space_after = Pt(4)

        # Predictive analysis slide
        slide = prs.slides.add_slide(slide_layout)
        title = slide.shapes.title
        content = slide.placeholders[1]

        title.text = "Predictive Analysis & Future Scenarios"

        if 'predictive_modeling' in self.analysis_results:
            prediction_data = self.analysis_results['predictive_modeling']

            prediction_details = ["🔮 Ranking Forecast Analysis:", ""]

            if 'ranking_forecast' in prediction_data and 'forecasts' in prediction_data['ranking_forecast']:
                forecasts = prediction_data['ranking_forecast']['forecasts']
                model_r2 = prediction_data['ranking_forecast'].get('model_r2', 0)

                prediction_details.extend([
                    f"📊 Model Performance: R² = {model_r2:.3f}",
                    "",
                    "📈 Ranking Forecasts:"
                ])

                for forecast in forecasts:
                    year = forecast['year']
                    predicted_rank = forecast['predicted_rank']
                    conf_lower = forecast['confidence_lower']
                    conf_upper = forecast['confidence_upper']

                    prediction_details.append(
                        f"   • {year}: Rank {predicted_rank:.0f} "
                        f"(95% CI: {conf_lower:.0f}-{conf_upper:.0f})"
                    )

                prediction_details.append("")

            # Classification transition analysis
            if 'classification_transitions' in self.analysis_results:
                transition_data = self.analysis_results['classification_transitions']

                if 'symbiosis_prediction' in transition_data:
                    prediction = transition_data['symbiosis_prediction']

                    prediction_details.extend([
                        "🔄 Classification Transition Impact (CO → FC):",
                        f"   • Historical Average Impact: {prediction.get('historical_average_impact', 'N/A')} positions",
                        f"   • Success Probability: {prediction.get('success_probability', 0)*100:.1f}%",
                        f"   • Recommendation: {prediction.get('recommendation', 'N/A')}"
                    ])

            content.text = "\n".join(prediction_details)

            # Format content
            for paragraph in content.text_frame.paragraphs:
                paragraph.font.size = Pt(13)
                paragraph.space_after = Pt(4)

    def add_recommendations_section(self, prs):
        """Add recommendations and conclusions slides"""

        # Data-driven observations slide
        slide_layout = prs.slide_layouts[1]
        slide = prs.slides.add_slide(slide_layout)

        title = slide.shapes.title
        content = slide.placeholders[1]

        title.text = "Data-Driven Observations"

        observations = [
            "📊 Performance Analysis Insights:",
            "",
            "✓ Symbiosis maintains consistent global ranking presence",
            "✓ Recent ranking decline requires strategic attention",
            "✓ Competitive position within private Indian sector is viable",
            "✓ Clear performance gaps identified vs superior performers",
            "",
            "📈 Strategic Opportunities:",
            "",
            "✓ High-impact metrics identified for focused improvement",
            "✓ Classification transition potential assessed",
            "✓ Predictive modeling provides future scenario planning",
            "✓ Benchmarking reveals specific improvement pathways",
            "",
            "🎯 Critical Success Factors:",
            "",
            "✓ Academic reputation enhancement",
            "✓ Research output optimization",
            "✓ International engagement expansion",
            "✓ Employer relationship strengthening"
        ]

        content.text = "\n".join(observations)

        # Format content with colors
        for i, paragraph in enumerate(content.text_frame.paragraphs):
            paragraph.font.size = Pt(14)
            paragraph.space_after = Pt(4)

            if paragraph.text.startswith("✓"):
                paragraph.font.color.rgb = self.colors['success']
            elif paragraph.text.startswith("📊") or paragraph.text.startswith("📈") or paragraph.text.startswith("🎯"):
                paragraph.font.color.rgb = self.colors['primary']

        # Key conclusions slide
        slide = prs.slides.add_slide(slide_layout)
        title = slide.shapes.title
        content = slide.placeholders[1]

        title.text = "Key Conclusions"

        conclusions = [
            "🎯 Strategic Position Assessment:",
            "",
            "• Symbiosis International University demonstrates resilient",
            "  performance in competitive global ranking environment",
            "",
            "• Current ranking trajectory requires focused intervention",
            "  to reverse declining trend and achieve improvement",
            "",
            "• Competitive analysis reveals clear pathways for",
            "  strategic enhancement and ranking advancement",
            "",
            "🔍 Analytical Insights:",
            "",
            "• Data-driven approach enables evidence-based",
            "  decision-making for institutional development",
            "",
            "• Predictive modeling provides valuable foresight",
            "  for strategic planning and resource allocation",
            "",
            "• Continuous monitoring and adaptive strategies",
            "  essential for sustained ranking improvement"
        ]

        content.text = "\n".join(conclusions)

        # Format content
        for paragraph in content.text_frame.paragraphs:
            paragraph.font.size = Pt(15)
            paragraph.space_after = Pt(6)

            if paragraph.text.startswith("🎯") or paragraph.text.startswith("🔍"):
                paragraph.font.color.rgb = self.colors['primary']
                paragraph.font.bold = True

    def add_appendix_section(self, prs):
        """Add appendix slides with technical details"""

        # Methodology slide
        slide_layout = prs.slide_layouts[1]
        slide = prs.slides.add_slide(slide_layout)

        title = slide.shapes.title
        content = slide.placeholders[1]

        title.text = "Appendix: Methodology & Data Sources"

        methodology_details = [
            "📋 Data Sources:",
            "",
            "• QS World University Rankings (2022-2026)",
            "• 7,224 institutional records across 5 years",
            "• 29 standardized metrics per institution",
            "• Global coverage with detailed Indian analysis",
            "",
            "🔬 Analytical Methods:",
            "",
            "• Descriptive statistics and trend analysis",
            "• Correlation analysis and regression modeling",
            "• Time series forecasting and predictive modeling",
            "• Comparative benchmarking analysis",
            "• Classification impact assessment",
            "",
            "✅ Quality Assurance:",
            "",
            "• Data validation and consistency checks",
            "• Cross-verification of key findings",
            "• Statistical significance testing",
            "• Professional peer review process"
        ]

        content.text = "\n".join(methodology_details)

        # Format content
        for paragraph in content.text_frame.paragraphs:
            paragraph.font.size = Pt(13)
            paragraph.space_after = Pt(4)

        # Contact information slide
        slide = prs.slides.add_slide(slide_layout)
        title = slide.shapes.title
        content = slide.placeholders[1]

        title.text = "Contact Information"

        contact_info = [
            "📧 For questions or additional analysis:",
            "",
            "Dr. Dharmendra Pandey",
            "Deputy Director - Quality Management & Benchmarking (QMB)",
            "Head - Quality Assurance (QA)",
            "Symbiosis International (Deemed University)",
            "Pune, India",
            "",
            "Email: <EMAIL>",
            "Email: <EMAIL>",
            "",
            f"Report Generated: {self.report_date}",
            "",
            "🔗 This analysis is based on comprehensive statistical",
            "   modeling and data-driven methodologies for",
            "   strategic institutional decision-making."
        ]

        content.text = "\n".join(contact_info)

        # Format content
        for paragraph in content.text_frame.paragraphs:
            paragraph.font.size = Pt(14)
            paragraph.space_after = Pt(6)
            paragraph.alignment = PP_ALIGN.CENTER


def main():
    """Main function to generate enhanced presentation"""
    print("Enhanced PowerPoint Presentation Generator")
    print("=" * 50)

    try:
        # Load analysis results
        from qs_wur_comprehensive_analysis import QSWURAnalyzer

        print("Running comprehensive analysis...")
        analyzer = QSWURAnalyzer()
        analysis_results = analyzer.run_comprehensive_analysis()

        # Generate enhanced presentation
        print("Creating enhanced presentation...")
        presentation_generator = EnhancedPresentationGenerator(analysis_results)
        presentation_path = presentation_generator.create_enhanced_presentation()

        print("\nEnhanced Presentation Generated Successfully!")
        print("-" * 50)
        print(f"Enhanced PowerPoint: {presentation_path}")

        return presentation_path

    except Exception as e:
        print(f"Error: {str(e)}")
        return None


if __name__ == "__main__":
    presentation_path = main()
