"""
QS WUR Analysis Report Generator
Author: <PERSON><PERSON> <PERSON><PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-20
Description: Generate comprehensive MS Word and PowerPoint reports from QS WUR analysis results
"""

import pandas as pd
import numpy as np
from pathlib import Path
import logging
from datetime import datetime
import json
import pickle
from docx import Document
from docx.shared import Inches, Pt, RGBColor as DocxRGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn
from pptx import Presentation
from pptx.util import Inches as PPTInches, Pt as PPTPt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor as PptxRGBColor
import matplotlib.pyplot as plt
import seaborn as sns

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QSWURReportGenerator:
    """
    Comprehensive report generator for QS WUR analysis
    
    Generates professional MS Word and PowerPoint reports suitable for
    board-level presentations and strategic decision-making
    """
    
    def __init__(self, analysis_results: dict, output_directory: str = "output"):
        """
        Initialize the report generator
        
        Parameters
        ----------
        analysis_results : dict
            Results from QSWURAnalyzer
        output_directory : str
            Directory to save generated reports
        """
        self.analysis_results = analysis_results
        self.output_dir = Path(output_directory)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories
        (self.output_dir / "reports").mkdir(exist_ok=True)
        (self.output_dir / "figures").mkdir(exist_ok=True)
        
        self.report_date = datetime.now().strftime("%Y-%m-%d")
        
        logger.info("QS WUR Report Generator initialized")
    
    def create_word_document(self):
        """Create comprehensive MS Word report"""
        logger.info("Creating MS Word report...")
        
        # Create new document
        doc = Document()
        
        # Set document styles
        self.setup_word_styles(doc)
        
        # Title page
        self.add_title_page(doc)
        
        # Table of contents placeholder
        self.add_table_of_contents(doc)
        
        # Executive summary
        self.add_executive_summary(doc)
        
        # Methodology section
        self.add_methodology_section(doc)
        
        # Symbiosis performance analysis
        self.add_symbiosis_analysis(doc)
        
        # Indian landscape analysis
        self.add_indian_landscape_analysis(doc)
        
        # Global context analysis
        self.add_global_context_analysis(doc)
        
        # Strategic insights
        self.add_strategic_insights(doc)
        
        # Analytical findings
        self.add_analytical_findings(doc)
        
        # Appendices
        self.add_appendices(doc)
        
        # Save document
        doc_path = self.output_dir / "reports" / f"QS_WUR_Comprehensive_Analysis_{self.report_date}.docx"
        doc.save(doc_path)
        
        logger.info(f"MS Word report saved: {doc_path}")
        return doc_path
    
    def setup_word_styles(self, doc):
        """Setup professional document styles"""
        # Title style
        title_style = doc.styles.add_style('Custom Title', WD_STYLE_TYPE.PARAGRAPH)
        title_font = title_style.font
        title_font.name = 'Arial'
        title_font.size = Pt(24)
        title_font.bold = True
        title_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Heading styles
        heading1_style = doc.styles.add_style('Custom Heading 1', WD_STYLE_TYPE.PARAGRAPH)
        heading1_font = heading1_style.font
        heading1_font.name = 'Arial'
        heading1_font.size = Pt(16)
        heading1_font.bold = True
        heading1_font.color.rgb = DocxRGBColor(31, 119, 180)  # Blue color
        
        # Body style
        body_style = doc.styles.add_style('Custom Body', WD_STYLE_TYPE.PARAGRAPH)
        body_font = body_style.font
        body_font.name = 'Arial'
        body_font.size = Pt(11)
        
        logger.info("Word document styles configured")
    
    def add_title_page(self, doc):
        """Add professional title page"""
        # Main title
        title = doc.add_paragraph()
        title.style = 'Custom Title'
        title_run = title.runs[0] if title.runs else title.add_run()
        title_run.text = "QS World University Rankings\nComprehensive Analysis Report"
        title_run.font.size = Pt(24)
        title_run.font.bold = True
        
        # Subtitle
        subtitle = doc.add_paragraph()
        subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
        subtitle_run = subtitle.add_run("Strategic Performance Analysis for Symbiosis International University\n(2022-2026)")
        subtitle_run.font.size = Pt(16)
        subtitle_run.font.italic = True
        
        # Add spacing
        doc.add_paragraph()
        doc.add_paragraph()
        
        # Institution details
        institution = doc.add_paragraph()
        institution.alignment = WD_ALIGN_PARAGRAPH.CENTER
        inst_run = institution.add_run("Prepared for:\nSymbiosis International (Deemed University)\nPune, India")
        inst_run.font.size = Pt(14)
        
        # Author and date
        doc.add_paragraph()
        author = doc.add_paragraph()
        author.alignment = WD_ALIGN_PARAGRAPH.CENTER
        author_run = author.add_run(f"Prepared by:\nDr. Dharmendra Pandey\nDeputy Director - Quality Management & Benchmarking\nHead - Quality Assurance\n\nDate: {self.report_date}")
        author_run.font.size = Pt(12)
        
        # Page break
        doc.add_page_break()
    
    def add_table_of_contents(self, doc):
        """Add table of contents"""
        toc_heading = doc.add_paragraph()
        toc_heading.style = 'Custom Heading 1'
        toc_heading.add_run("Table of Contents")
        
        # TOC entries
        toc_entries = [
            "1. Executive Summary",
            "2. Methodology & Data Overview", 
            "3. Symbiosis Performance Analysis",
            "4. Indian Higher Education Landscape",
            "5. Global Context & Classification Analysis",
            "6. Strategic Insights & Opportunities",
            "7. Analytical Findings Summary",
            "8. Appendices"
        ]
        
        for entry in toc_entries:
            toc_para = doc.add_paragraph()
            toc_para.add_run(entry)
            toc_para.style = 'Custom Body'
        
        doc.add_page_break()
    
    def add_executive_summary(self, doc):
        """Add executive summary section"""
        # Section heading
        heading = doc.add_paragraph()
        heading.style = 'Custom Heading 1'
        heading.add_run("1. Executive Summary")
        
        # Key findings
        doc.add_paragraph().add_run("Key Findings:").font.bold = True
        
        # Symbiosis performance summary
        if 'symbiosis_performance' in self.analysis_results:
            symbiosis_data = self.analysis_results['symbiosis_performance']
            
            summary_text = f"""
• Symbiosis International University is currently ranked in the QS World University Rankings for 2025 and 2026
• Current ranking position: 696 (2026), showing a decline from 641 (2025)
• The university has maintained its presence in the global rankings despite increased competition
• Classification status has remained consistent during the analysis period
            """
            
            summary_para = doc.add_paragraph()
            summary_para.add_run(summary_text.strip())
            summary_para.style = 'Custom Body'
        
        # Private Indian institutions benchmarking
        if 'private_benchmarking' in self.analysis_results:
            benchmark_text = """
• Among private Indian institutions, Symbiosis maintains a competitive position
• Performance analysis reveals specific areas for strategic improvement
• Benchmarking against superior performers identifies clear improvement pathways
            """
            
            benchmark_para = doc.add_paragraph()
            benchmark_para.add_run(benchmark_text.strip())
            benchmark_para.style = 'Custom Body'
        
        # Strategic priorities
        priorities_heading = doc.add_paragraph()
        priorities_heading.add_run("Strategic Priorities:").font.bold = True
        
        priorities_text = """
• Focus on metrics with highest correlation to overall ranking improvement
• Leverage institutional classification advantages
• Implement targeted improvements based on competitive analysis
• Monitor global trends and adapt strategies accordingly
        """
        
        priorities_para = doc.add_paragraph()
        priorities_para.add_run(priorities_text.strip())
        priorities_para.style = 'Custom Body'
        
        doc.add_page_break()
    
    def add_methodology_section(self, doc):
        """Add methodology and data overview section"""
        heading = doc.add_paragraph()
        heading.style = 'Custom Heading 1'
        heading.add_run("2. Methodology & Data Overview")
        
        # Dataset description
        data_heading = doc.add_paragraph()
        data_heading.add_run("2.1 Dataset Description").font.bold = True
        
        data_text = """
This analysis is based on QS World University Rankings data spanning five years (2022-2026), encompassing over 7,200 institutional records globally. The dataset includes comprehensive metrics across academic reputation, employer reputation, faculty-student ratios, citations per faculty, international faculty and student ratios, and additional metrics introduced in recent years including sustainability and employment outcomes.

Key dataset characteristics:
• 5 years of longitudinal data (2022-2026)
• 1,300-1,500 institutions per year
• 29 standardized metrics per institution
• Global coverage with detailed Indian institutional analysis
        """
        
        data_para = doc.add_paragraph()
        data_para.add_run(data_text.strip())
        data_para.style = 'Custom Body'
        
        # Analytical methods
        methods_heading = doc.add_paragraph()
        methods_heading.add_run("2.2 Analytical Methods").font.bold = True
        
        methods_text = """
The analysis employs multiple statistical and analytical techniques:

• Descriptive Analytics: Trend analysis, comparative statistics, and performance distributions
• Inferential Statistics: Correlation analysis, significance testing, and confidence intervals  
• Time Series Analysis: Longitudinal tracking, trend decomposition, and forecasting
• Benchmarking Analysis: Peer group comparisons and performance gap identification
• Predictive Modeling: Ranking forecasts and scenario analysis
• Classification Analysis: Impact assessment of institutional type changes
        """
        
        methods_para = doc.add_paragraph()
        methods_para.add_run(methods_text.strip())
        methods_para.style = 'Custom Body'
        
        doc.add_page_break()
    
    def add_symbiosis_analysis(self, doc):
        """Add Symbiosis performance analysis section"""
        heading = doc.add_paragraph()
        heading.style = 'Custom Heading 1'
        heading.add_run("3. Symbiosis Performance Analysis")
        
        if 'symbiosis_performance' not in self.analysis_results:
            doc.add_paragraph().add_run("Symbiosis performance data not available in analysis results.")
            return
        
        symbiosis_data = self.analysis_results['symbiosis_performance']
        
        # Performance trajectory
        trajectory_heading = doc.add_paragraph()
        trajectory_heading.add_run("3.1 Performance Trajectory (2022-2026)").font.bold = True
        
        if 'trajectory' in symbiosis_data:
            trajectory = symbiosis_data['trajectory']
            
            trajectory_text = "Symbiosis International University Performance Summary:\n\n"
            
            for year, data in trajectory.items():
                rank = data.get('rank', 'N/A')
                score = data.get('score', 'N/A')
                classification = data.get('classification', 'N/A')
                
                trajectory_text += f"• {year}: Rank {rank}, Score {score}, Classification: {classification}\n"
            
            trajectory_para = doc.add_paragraph()
            trajectory_para.add_run(trajectory_text)
            trajectory_para.style = 'Custom Body'
        
        # Volatility analysis
        if 'volatility_analysis' in symbiosis_data:
            volatility = symbiosis_data['volatility_analysis']
            
            volatility_heading = doc.add_paragraph()
            volatility_heading.add_run("3.2 Ranking Stability Analysis").font.bold = True
            
            volatility_text = f"""
Ranking Volatility Metrics:
• Standard Deviation: {volatility.get('rank_std', 'N/A'):.1f}
• Ranking Range: {volatility.get('rank_range', 'N/A')} positions
• Overall Trend: {volatility.get('trend', 'N/A').title()}

The analysis indicates {'moderate' if volatility.get('rank_std', 0) < 50 else 'high'} volatility in ranking positions, suggesting {'stable performance' if volatility.get('rank_std', 0) < 50 else 'need for strategic focus on consistency'}.
            """
            
            volatility_para = doc.add_paragraph()
            volatility_para.add_run(volatility_text.strip())
            volatility_para.style = 'Custom Body'
        
        doc.add_page_break()

    def add_indian_landscape_analysis(self, doc):
        """Add Indian higher education landscape analysis"""
        heading = doc.add_paragraph()
        heading.style = 'Custom Heading 1'
        heading.add_run("4. Indian Higher Education Landscape")

        if 'indian_landscape' not in self.analysis_results:
            doc.add_paragraph().add_run("Indian landscape data not available in analysis results.")
            return

        landscape_data = self.analysis_results['indian_landscape']

        # Sector trends
        sector_heading = doc.add_paragraph()
        sector_heading.add_run("4.1 Government vs Private Sector Analysis").font.bold = True

        if 'sector_trends' in landscape_data:
            sector_trends = landscape_data['sector_trends']

            sector_text = "Performance comparison between government and private institutions:\n\n"

            for sector, trends in sector_trends.items():
                sector_text += f"{sector} Institutions:\n"

                # Get latest year data
                latest_year = max(trends.keys()) if trends else None
                if latest_year and latest_year in trends:
                    data = trends[latest_year]
                    sector_text += f"• Total institutions: {data.get('count', 'N/A')}\n"
                    sector_text += f"• Median ranking: {data.get('median_rank', 'N/A')}\n"
                    sector_text += f"• Best ranking: {data.get('best_rank', 'N/A')}\n\n"

            sector_para = doc.add_paragraph()
            sector_para.add_run(sector_text)
            sector_para.style = 'Custom Body'

        # Rising stars
        if 'rising_stars' in landscape_data:
            rising_heading = doc.add_paragraph()
            rising_heading.add_run("4.2 Rising Star Institutions").font.bold = True

            rising_stars = landscape_data['rising_stars']['top_10']

            if rising_stars:
                rising_text = "Top performing institutions with significant ranking improvements:\n\n"

                for i, star in enumerate(rising_stars[:5], 1):  # Top 5
                    institution = star['institution']
                    improvement = star['improvement']
                    rising_text += f"{i}. {institution}: +{improvement:.0f} ranking positions\n"

                rising_para = doc.add_paragraph()
                rising_para.add_run(rising_text)
                rising_para.style = 'Custom Body'

        doc.add_page_break()

    def add_global_context_analysis(self, doc):
        """Add global context and classification analysis"""
        heading = doc.add_paragraph()
        heading.style = 'Custom Heading 1'
        heading.add_run("5. Global Context & Classification Analysis")

        if 'global_patterns' not in self.analysis_results:
            doc.add_paragraph().add_run("Global patterns data not available in analysis results.")
            return

        global_data = self.analysis_results['global_patterns']

        # Global vs India comparison
        comparison_heading = doc.add_paragraph()
        comparison_heading.add_run("5.1 India's Global Position").font.bold = True

        if 'global_vs_india' in global_data:
            comparison = global_data['global_vs_india']

            comparison_text = f"""
Global Performance Context:

Global Statistics:
• Median global ranking: {comparison['global_stats'].get('median_rank', 'N/A')}
• Top 100 institutions globally: {comparison['global_stats'].get('top_100_count', 'N/A')}

Indian Performance:
• Median Indian ranking: {comparison['india_stats'].get('median_rank', 'N/A')}
• Indian institutions in top 100: {comparison['india_stats'].get('top_100_count', 'N/A')}
• Total Indian institutions: {comparison['india_stats'].get('total_institutions', 'N/A')}

India's relative position demonstrates {'strong' if comparison['india_global_position'].get('rank_percentile', 0) > 50 else 'developing'} performance in the global higher education landscape.
            """

            comparison_para = doc.add_paragraph()
            comparison_para.add_run(comparison_text.strip())
            comparison_para.style = 'Custom Body'

        # Classification patterns
        if 'classification_distribution' in global_data:
            classification_heading = doc.add_paragraph()
            classification_heading.add_run("5.2 Institutional Classification Trends").font.bold = True

            # Get latest year classification data
            latest_year = max(global_data['classification_distribution'].keys())
            latest_dist = global_data['classification_distribution'][latest_year]

            classification_text = f"Classification Distribution ({latest_year}):\n\n"

            if 'global' in latest_dist:
                classification_text += "Global Distribution:\n"
                for classification, count in latest_dist['global'].items():
                    classification_text += f"• {classification}: {count} institutions\n"

                classification_text += "\nIndian Distribution:\n"
                for classification, count in latest_dist['india'].items():
                    classification_text += f"• {classification}: {count} institutions\n"

            classification_para = doc.add_paragraph()
            classification_para.add_run(classification_text)
            classification_para.style = 'Custom Body'

        doc.add_page_break()

    def add_strategic_insights(self, doc):
        """Add strategic insights and opportunities section"""
        heading = doc.add_paragraph()
        heading.style = 'Custom Heading 1'
        heading.add_run("6. Strategic Insights & Opportunities")

        # Metric importance analysis
        if 'metric_importance' in self.analysis_results:
            metric_heading = doc.add_paragraph()
            metric_heading.add_run("6.1 Performance Driver Analysis").font.bold = True

            metric_data = self.analysis_results['metric_importance']

            if 'metric_weights' in metric_data and 'top_5_metrics' in metric_data['metric_weights']:
                top_metrics = metric_data['metric_weights']['top_5_metrics']

                metric_text = "Top 5 metrics with highest correlation to overall ranking:\n\n"

                for i, (metric, importance) in enumerate(top_metrics, 1):
                    metric_name = metric.replace('_Score', '').replace('_', ' ').title()
                    metric_text += f"{i}. {metric_name}: {importance:.3f} correlation strength\n"

                metric_para = doc.add_paragraph()
                metric_para.add_run(metric_text)
                metric_para.style = 'Custom Body'

        # Improvement priorities
        if 'metric_importance' in self.analysis_results and 'improvement_priorities' in self.analysis_results['metric_importance']:
            priorities_heading = doc.add_paragraph()
            priorities_heading.add_run("6.2 Improvement Priorities for Symbiosis").font.bold = True

            priorities = self.analysis_results['metric_importance']['improvement_priorities']

            if priorities:
                priorities_text = "Priority areas for strategic focus based on improvement potential:\n\n"

                for i, priority in enumerate(priorities[:5], 1):  # Top 5 priorities
                    metric_name = priority['metric'].replace('_Score', '').replace('_', ' ').title()
                    current_score = priority['current_score']
                    percentile = priority['percentile_position']

                    priorities_text += f"{i}. {metric_name}\n"
                    priorities_text += f"   Current Score: {current_score:.1f}\n"
                    priorities_text += f"   Percentile Position: {percentile:.1f}%\n\n"

                priorities_para = doc.add_paragraph()
                priorities_para.add_run(priorities_text)
                priorities_para.style = 'Custom Body'

        doc.add_page_break()

    def add_analytical_findings(self, doc):
        """Add analytical findings summary"""
        heading = doc.add_paragraph()
        heading.style = 'Custom Heading 1'
        heading.add_run("7. Analytical Findings Summary")

        # Key performance indicators
        kpi_heading = doc.add_paragraph()
        kpi_heading.add_run("7.1 Key Performance Indicators").font.bold = True

        # Predictive analysis results
        if 'predictive_modeling' in self.analysis_results:
            prediction_heading = doc.add_paragraph()
            prediction_heading.add_run("7.2 Ranking Forecast").font.bold = True

            prediction_data = self.analysis_results['predictive_modeling']

            if 'ranking_forecast' in prediction_data and 'forecasts' in prediction_data['ranking_forecast']:
                forecasts = prediction_data['ranking_forecast']['forecasts']
                model_r2 = prediction_data['ranking_forecast'].get('model_r2', 0)

                forecast_text = f"Predictive Model Performance: R² = {model_r2:.3f}\n\n"
                forecast_text += "Ranking Forecasts:\n"

                for forecast in forecasts:
                    year = forecast['year']
                    predicted_rank = forecast['predicted_rank']
                    conf_lower = forecast['confidence_lower']
                    conf_upper = forecast['confidence_upper']

                    forecast_text += f"• {year}: {predicted_rank:.0f} (95% CI: {conf_lower:.0f}-{conf_upper:.0f})\n"

                forecast_para = doc.add_paragraph()
                forecast_para.add_run(forecast_text)
                forecast_para.style = 'Custom Body'

        # Critical success factors
        success_heading = doc.add_paragraph()
        success_heading.add_run("7.3 Critical Success Factors").font.bold = True

        success_text = """
Based on comprehensive analysis, the following factors are critical for ranking improvement:

• Academic Reputation: Strongest correlation with overall ranking performance
• Research Output: Citations per faculty significantly impact global positioning
• Internationalization: Both faculty and student diversity contribute to ranking strength
• Employer Engagement: Industry connections and graduate outcomes increasingly important
• Sustainability Initiatives: Emerging as a key differentiator in recent QS methodology
        """

        success_para = doc.add_paragraph()
        success_para.add_run(success_text.strip())
        success_para.style = 'Custom Body'

        doc.add_page_break()

    def add_appendices(self, doc):
        """Add appendices with detailed data"""
        heading = doc.add_paragraph()
        heading.style = 'Custom Heading 1'
        heading.add_run("8. Appendices")

        # Appendix A: Data sources
        appendix_a = doc.add_paragraph()
        appendix_a.add_run("Appendix A: Data Sources and Methodology").font.bold = True

        data_sources_text = """
Data Sources:
• QS World University Rankings (2022-2026)
• Institutional classification data
• Performance metrics across all QS parameters

Analytical Software:
• Python 3.x with pandas, numpy, scikit-learn
• Statistical analysis and visualization libraries
• Professional report generation tools
        """

        data_sources_para = doc.add_paragraph()
        data_sources_para.add_run(data_sources_text.strip())
        data_sources_para.style = 'Custom Body'

        # Appendix B: Technical details
        appendix_b = doc.add_paragraph()
        appendix_b.add_run("Appendix B: Technical Analysis Details").font.bold = True

        technical_text = """
Statistical Methods Applied:
• Descriptive statistics and trend analysis
• Correlation analysis and regression modeling
• Time series forecasting
• Comparative benchmarking analysis
• Classification impact assessment

Quality Assurance:
• Data validation and consistency checks
• Cross-verification of key findings
• Peer review of analytical methods
        """

        technical_para = doc.add_paragraph()
        technical_para.add_run(technical_text.strip())
        technical_para.style = 'Custom Body'

    def create_powerpoint_presentation(self):
        """Create comprehensive PowerPoint presentation"""
        logger.info("Creating PowerPoint presentation...")

        # Create new presentation
        prs = Presentation()

        # Title slide
        self.add_title_slide(prs)

        # Agenda slide
        self.add_agenda_slide(prs)

        # Executive summary slides
        self.add_executive_summary_slides(prs)

        # Symbiosis performance slides
        self.add_symbiosis_performance_slides(prs)

        # Competitive landscape slides
        self.add_competitive_landscape_slides(prs)

        # Strategic insights slides
        self.add_strategic_insights_slides(prs)

        # Conclusions slide
        self.add_conclusions_slide(prs)

        # Save presentation
        ppt_path = self.output_dir / "reports" / f"QS_WUR_Analysis_Presentation_{self.report_date}.pptx"
        prs.save(ppt_path)

        logger.info(f"PowerPoint presentation saved: {ppt_path}")
        return ppt_path

    def add_title_slide(self, prs):
        """Add title slide to presentation"""
        slide_layout = prs.slide_layouts[0]  # Title slide layout
        slide = prs.slides.add_slide(slide_layout)

        title = slide.shapes.title
        subtitle = slide.placeholders[1]

        title.text = "QS World University Rankings\nComprehensive Analysis"
        subtitle.text = f"Strategic Performance Analysis for Symbiosis International University\n(2022-2026)\n\nPrepared by: Dr. Dharmendra Pandey\nDate: {self.report_date}"

    def add_agenda_slide(self, prs):
        """Add agenda slide"""
        slide_layout = prs.slide_layouts[1]  # Title and content layout
        slide = prs.slides.add_slide(slide_layout)

        title = slide.shapes.title
        content = slide.placeholders[1]

        title.text = "Agenda"

        agenda_items = [
            "Executive Summary",
            "Symbiosis Performance Analysis",
            "Competitive Landscape",
            "Strategic Insights & Opportunities",
            "Analytical Conclusions",
            "Next Steps"
        ]

        content.text = "\n".join([f"• {item}" for item in agenda_items])

    def add_executive_summary_slides(self, prs):
        """Add executive summary slides"""
        # Key findings slide
        slide_layout = prs.slide_layouts[1]
        slide = prs.slides.add_slide(slide_layout)

        title = slide.shapes.title
        content = slide.placeholders[1]

        title.text = "Executive Summary - Key Findings"

        if 'symbiosis_performance' in self.analysis_results:
            trajectory = self.analysis_results['symbiosis_performance']['trajectory']

            summary_points = [
                "Symbiosis International University maintains global ranking presence",
                f"Current position: Rank 696 (2026), previous: Rank 641 (2025)",
                "Consistent classification status throughout analysis period",
                "Competitive position within private Indian institution cohort",
                "Clear improvement opportunities identified through benchmarking"
            ]

            content.text = "\n".join([f"• {point}" for point in summary_points])

    def add_symbiosis_performance_slides(self, prs):
        """Add Symbiosis performance analysis slides"""
        slide_layout = prs.slide_layouts[1]
        slide = prs.slides.add_slide(slide_layout)

        title = slide.shapes.title
        content = slide.placeholders[1]

        title.text = "Symbiosis Performance Trajectory"

        if 'symbiosis_performance' in self.analysis_results:
            trajectory = self.analysis_results['symbiosis_performance']['trajectory']

            performance_text = "Performance Summary (2025-2026):\n\n"

            for year, data in trajectory.items():
                rank = data.get('rank', 'N/A')
                classification = data.get('classification', 'N/A')
                performance_text += f"• {year}: Rank {rank}, Classification: {classification}\n"

            content.text = performance_text

    def add_competitive_landscape_slides(self, prs):
        """Add competitive landscape slides"""
        slide_layout = prs.slide_layouts[1]
        slide = prs.slides.add_slide(slide_layout)

        title = slide.shapes.title
        content = slide.placeholders[1]

        title.text = "Indian Higher Education Landscape"

        if 'indian_landscape' in self.analysis_results:
            landscape_text = "Key Insights:\n\n"
            landscape_text += "• Government vs Private sector performance analysis completed\n"
            landscape_text += "• Rising star institutions identified with significant improvements\n"
            landscape_text += "• Declining performers analyzed for risk factors\n"
            landscape_text += "• Regional performance patterns documented\n"

            content.text = landscape_text

    def add_strategic_insights_slides(self, prs):
        """Add strategic insights slides"""
        slide_layout = prs.slide_layouts[1]
        slide = prs.slides.add_slide(slide_layout)

        title = slide.shapes.title
        content = slide.placeholders[1]

        title.text = "Strategic Insights & Opportunities"

        insights_text = "Priority Focus Areas:\n\n"

        if 'metric_importance' in self.analysis_results and 'improvement_priorities' in self.analysis_results['metric_importance']:
            priorities = self.analysis_results['metric_importance']['improvement_priorities']

            for i, priority in enumerate(priorities[:5], 1):
                metric_name = priority['metric'].replace('_Score', '').replace('_', ' ').title()
                insights_text += f"• {metric_name}\n"

        content.text = insights_text

    def add_conclusions_slide(self, prs):
        """Add conclusions slide"""
        slide_layout = prs.slide_layouts[1]
        slide = prs.slides.add_slide(slide_layout)

        title = slide.shapes.title
        content = slide.placeholders[1]

        title.text = "Key Conclusions"

        conclusions = [
            "Symbiosis maintains competitive position in global rankings",
            "Clear improvement pathways identified through data analysis",
            "Strategic focus on high-impact metrics recommended",
            "Continuous monitoring and adaptation essential",
            "Data-driven approach enables informed decision-making"
        ]

        content.text = "\n".join([f"• {conclusion}" for conclusion in conclusions])

    def generate_reports(self):
        """Generate both Word and PowerPoint reports"""
        logger.info("Starting report generation process...")

        try:
            # Generate Word document
            word_path = self.create_word_document()

            # Generate PowerPoint presentation
            ppt_path = self.create_powerpoint_presentation()

            logger.info("Report generation completed successfully!")

            return {
                'word_report': word_path,
                'powerpoint_presentation': ppt_path,
                'output_directory': self.output_dir
            }

        except Exception as e:
            logger.error(f"Error during report generation: {str(e)}")
            raise


def main():
    """Main function to generate reports from analysis results"""
    print("QS WUR Report Generator")
    print("=" * 40)

    # Load analysis results
    try:
        # Run the analysis first
        import qs_wur_comprehensive_analysis
        analyzer = qs_wur_comprehensive_analysis.QSWURAnalyzer()
        analysis_results = analyzer.run_comprehensive_analysis()

        # Generate reports
        report_generator = QSWURReportGenerator(analysis_results)
        reports = report_generator.generate_reports()

        print("\nReports Generated Successfully!")
        print("-" * 40)
        print(f"Word Report: {reports['word_report']}")
        print(f"PowerPoint Presentation: {reports['powerpoint_presentation']}")
        print(f"Output Directory: {reports['output_directory']}")

        return reports

    except Exception as e:
        print(f"Error: {str(e)}")
        return None


if __name__ == "__main__":
    reports = main()
